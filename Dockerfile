# Multi-stage Dockerfile for Django application

# Base stage with common dependencies
FROM python:3.10-slim AS base
WORKDIR /app

# Install system dependencies needed for all stages
RUN apt-get update && apt-get install -y \
    libpq-dev \
    gcc \
    redis-tools \
    wget \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Create non-root user and group
RUN groupadd -g 1000 celery && \
    useradd -u 1000 -g celery -m -s /bin/bash celery

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1
ENV DOCKER_CONTAINER=1

# Dependencies stage - install Python packages
FROM base AS deps
COPY requirements.txt .
RUN pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir -r requirements.txt

# Build stage - prepare application files
FROM base AS build
WORKDIR /app

# Copy source code
COPY . .

# Create necessary directories
RUN mkdir -p /app/heibooky/staticfiles \
    /app/heibooky/media \
    /app/heibooky/logs

# Skip static collection in build stage to avoid dependency issues
# Static files will be collected at runtime if needed

# Production stage - final runtime image
FROM base AS production

# Copy Python dependencies from deps stage (wholesale copy is more efficient)
COPY --from=deps /usr/local /usr/local

# Copy application code and prepared files from build stage
COPY --from=build /app .

# Create runtime directories and set permissions
RUN mkdir -p /app/heibooky/staticfiles \
    /app/heibooky/media \
    /app/heibooky/logs \
    /var/run/celery \
    /var/log/celery \
    /var/log/heibooky && \
    touch /var/log/heibooky/django.log \
    /var/log/heibooky/error.log \
    /var/log/heibooky/celery.log && \
    chown -R celery:celery /app \
    /var/run/celery \
    /var/log/celery \
    /var/log/heibooky && \
    chmod -R 755 /app/heibooky/staticfiles \
    /app/heibooky/media \
    /app/heibooky/logs \
    /var/log/heibooky

# Expose port
EXPOSE 8000

# Switch to non-root user
USER celery

# Default command for web service
CMD ["sh", "-c", "\
    cd heibooky && \
    python manage.py collectstatic --noinput --clear && \
    python manage.py migrate && \
    exec daphne -b 0.0.0.0 -p 8000 heibooky.asgi:application"]