# Alertmanager Configuration
global:
  smtp_smarthost: '${SMTP_HOST}:${SMTP_PORT}'
  smtp_from: '${SMTP_FROM}'
  smtp_auth_username: '${SMTP_USERNAME}'
  smtp_auth_password: '${SMTP_PASSWORD}'
  smtp_auth_identity: '${SMTP_FROM}'
  smtp_require_tls: true
  http_config:
    tls_config:
      insecure_skip_verify: false
  resolve_timeout: 5m

templates:
  - '/etc/alertmanager/templates/*.tmpl'

route:
  group_by: ['alertname', 'cluster', 'service']
  group_wait: 30s
  group_interval: 5m
  repeat_interval: 12h
  receiver: 'default-receiver'
  routes:
    # SLO-based alerts - highest priority
    - match:
        slo: availability
        severity: critical
      receiver: 'slo-critical-alerts'
      group_wait: 0s
      group_interval: 1m
      repeat_interval: 5m
      continue: true

    - match:
        slo: latency
        severity: critical
      receiver: 'slo-critical-alerts'
      group_wait: 0s
      group_interval: 1m
      repeat_interval: 5m
      continue: true

    # Business impact alerts - immediate escalation
    - match:
        impact: business_critical
      receiver: 'business-critical-alerts'
      group_wait: 0s
      group_interval: 30s
      repeat_interval: 2m
      continue: true

    - match:
        impact: revenue
      receiver: 'revenue-impact-alerts'
      group_wait: 0s
      group_interval: 30s
      repeat_interval: 1m
      continue: true

    # Multi-window alerts - immediate attention
    - match:
        alert_type: multi_window
      receiver: 'multi-window-alerts'
      group_wait: 0s
      group_interval: 1m
      repeat_interval: 3m
      continue: true

    # Escalation alerts - management notification
    - match:
        escalation: level_2
      receiver: 'escalation-alerts'
      group_wait: 0s
      group_interval: 2m
      repeat_interval: 10m
      continue: true

    # Security alerts - immediate notification
    - match:
        alert_type: security
      receiver: 'security-alerts'
      group_wait: 30s
      group_interval: 1m
      repeat_interval: 15m
      continue: true

    # Capacity planning alerts
    - match:
        alert_type: capacity
      receiver: 'capacity-alerts'
      group_wait: 5m
      group_interval: 30m
      repeat_interval: 6h

    # Grouped alerts - reduce noise
    - match:
        alert_type: grouped
      receiver: 'grouped-alerts'
      group_wait: 2m
      group_interval: 10m
      repeat_interval: 30m

    # Critical alerts - immediate notification
    - match:
        severity: critical
      receiver: 'critical-alerts'
      group_wait: 10s
      group_interval: 2m
      repeat_interval: 30m
      continue: true

    # Warning alerts - less frequent
    - match:
        severity: warning
      receiver: 'warning-alerts'
      group_wait: 2m
      group_interval: 10m
      repeat_interval: 6h

    # Info alerts - daily digest
    - match:
        severity: info
      receiver: 'info-alerts'
      group_wait: 10m
      group_interval: 1h
      repeat_interval: 24h

receivers:
  - name: 'default-receiver'
    webhook_configs:
      - url: 'http://web:8000/monitoring/webhook/'
        send_resolved: true
        http_config:
          tls_config:
            insecure_skip_verify: false
        max_alerts: 0

  - name: 'slo-critical-alerts'
    email_configs:
      - to: '${ALERT_EMAIL_SLO}'
        from: '${SMTP_FROM}'
        subject: '🚨 CRITICAL SLO VIOLATION: {{ .GroupLabels.alertname }}'
        html: |
          <!DOCTYPE html>
          <html>
          <head>
              <meta charset="UTF-8">
              <title>Critical SLO Violation</title>
              <style>
                  body { font-family: Arial, sans-serif; margin: 20px; background-color: #fff5f5; }
                  .alert { background-color: #fed7d7; border: 2px solid #fc8181; padding: 20px; margin: 15px 0; border-radius: 8px; }
                  .slo-critical { color: #742a2a; }
                  .metrics { background-color: #f7fafc; padding: 15px; border-radius: 5px; margin: 10px 0; }
                  .runbook { background-color: #e6fffa; padding: 10px; border-radius: 5px; margin: 10px 0; }
              </style>
          </head>
          <body>
              <h1>🚨 CRITICAL SLO VIOLATION</h1>
              <p><strong>⚠️ IMMEDIATE ACTION REQUIRED</strong></p>

              {{ range .Alerts }}
              <div class="alert slo-critical">
                  <h2>{{ .Annotations.summary }}</h2>
                  <div class="metrics">
                      <p><strong>SLO Type:</strong> {{ .Labels.slo }}</p>
                      <p><strong>Burn Rate:</strong> {{ .Labels.burn_rate }}</p>
                      <p><strong>Severity:</strong> {{ .Labels.severity }}</p>
                      <p><strong>Instance:</strong> {{ .Labels.instance }}</p>
                  </div>
                  <p><strong>Description:</strong> {{ .Annotations.description }}</p>
                  {{ if .Annotations.runbook_url }}
                  <div class="runbook">
                      <p><strong>📖 Runbook:</strong> <a href="{{ .Annotations.runbook_url }}">{{ .Annotations.runbook_url }}</a></p>
                  </div>
                  {{ end }}
                  <p><strong>Started:</strong> {{ .StartsAt.Format "2006-01-02 15:04:05 UTC" }}</p>
              </div>
              {{ end }}

              <p><strong>Next Steps:</strong></p>
              <ol>
                  <li>Check the runbook for immediate remediation steps</li>
                  <li>Assess impact on user experience</li>
                  <li>Implement emergency response if needed</li>
                  <li>Update incident status in communication channels</li>
              </ol>
          </body>
          </html>
        headers:
          X-Priority: '1'
          X-MSMail-Priority: 'High'
          Importance: 'high'
    webhook_configs:
      - url: 'http://web:8000/monitoring/webhook/slo-critical'
        send_resolved: true

  - name: 'business-critical-alerts'
    email_configs:
      - to: '${ALERT_EMAIL_BUSINESS}'
        from: '${SMTP_FROM}'
        subject: '💼 BUSINESS CRITICAL: {{ .GroupLabels.alertname }}'
        html: |
          <!DOCTYPE html>
          <html>
          <head>
              <meta charset="UTF-8">
              <title>Business Critical Alert</title>
              <style>
                  body { font-family: Arial, sans-serif; margin: 20px; background-color: #fffaf0; }
                  .alert { background-color: #fed7aa; border: 2px solid #f6ad55; padding: 20px; margin: 15px 0; border-radius: 8px; }
                  .business-critical { color: #744210; }
                  .impact { background-color: #fef5e7; padding: 15px; border-radius: 5px; margin: 10px 0; }
              </style>
          </head>
          <body>
              <h1>💼 BUSINESS CRITICAL ALERT</h1>
              <p><strong>🔥 REVENUE/CUSTOMER IMPACT DETECTED</strong></p>

              {{ range .Alerts }}
              <div class="alert business-critical">
                  <h2>{{ .Annotations.summary }}</h2>
                  <div class="impact">
                      <p><strong>Business Impact:</strong> {{ .Labels.impact }}</p>
                      <p><strong>Severity:</strong> {{ .Labels.severity }}</p>
                      <p><strong>Instance:</strong> {{ .Labels.instance }}</p>
                  </div>
                  <p><strong>Description:</strong> {{ .Annotations.description }}</p>
                  <p><strong>Started:</strong> {{ .StartsAt.Format "2006-01-02 15:04:05 UTC" }}</p>
              </div>
              {{ end }}
          </body>
          </html>
        headers:
          X-Priority: '1'
          X-MSMail-Priority: 'High'
          Importance: 'high'

  - name: 'revenue-impact-alerts'
    email_configs:
      - to: '${ALERT_EMAIL_REVENUE}'
        from: '${SMTP_FROM}'
        subject: '💰 REVENUE IMPACT: {{ .GroupLabels.alertname }}'
        html: |
          <!DOCTYPE html>
          <html>
          <head>
              <meta charset="UTF-8">
              <title>Revenue Impact Alert</title>
              <style>
                  body { font-family: Arial, sans-serif; margin: 20px; background-color: #fff0f0; }
                  .alert { background-color: #fbb6ce; border: 2px solid #f687b3; padding: 20px; margin: 15px 0; border-radius: 8px; }
                  .revenue-impact { color: #702459; }
              </style>
          </head>
          <body>
              <h1>💰 REVENUE IMPACT ALERT</h1>
              <p><strong>💸 PAYMENT SYSTEM AFFECTED</strong></p>

              {{ range .Alerts }}
              <div class="alert revenue-impact">
                  <h2>{{ .Annotations.summary }}</h2>
                  <p><strong>Description:</strong> {{ .Annotations.description }}</p>
                  <p><strong>Started:</strong> {{ .StartsAt.Format "2006-01-02 15:04:05 UTC" }}</p>
              </div>
              {{ end }}
          </body>
          </html>
        headers:
          X-Priority: '1'
          X-MSMail-Priority: 'High'
          Importance: 'high'

  - name: 'multi-window-alerts'
    email_configs:
      - to: '${ALERT_EMAIL_CRITICAL}'
        from: '${SMTP_FROM}'
        subject: '⚡ MULTI-WINDOW ALERT: {{ .GroupLabels.alertname }}'
        html: |
          <!DOCTYPE html>
          <html>
          <head>
              <meta charset="UTF-8">
              <title>Multi-Window Alert</title>
              <style>
                  body { font-family: Arial, sans-serif; margin: 20px; background-color: #f0fff4; }
                  .alert { background-color: #c6f6d5; border: 2px solid #68d391; padding: 20px; margin: 15px 0; border-radius: 8px; }
                  .multi-window { color: #22543d; }
              </style>
          </head>
          <body>
              <h1>⚡ MULTI-WINDOW ALERT</h1>
              <p><strong>🎯 SUSTAINED ISSUE DETECTED</strong></p>

              {{ range .Alerts }}
              <div class="alert multi-window">
                  <h2>{{ .Annotations.summary }}</h2>
                  <p><strong>Alert Type:</strong> {{ .Labels.alert_type }}</p>
                  <p><strong>SLO:</strong> {{ .Labels.slo }}</p>
                  <p><strong>Description:</strong> {{ .Annotations.description }}</p>
                  <p><strong>Started:</strong> {{ .StartsAt.Format "2006-01-02 15:04:05 UTC" }}</p>
              </div>
              {{ end }}
          </body>
          </html>

  - name: 'escalation-alerts'
    email_configs:
      - to: '${ALERT_EMAIL_ESCALATION}'
        from: '${SMTP_FROM}'
        subject: '📈 ESCALATED ALERT: {{ .GroupLabels.alertname }}'
        html: |
          <!DOCTYPE html>
          <html>
          <head>
              <meta charset="UTF-8">
              <title>Escalated Alert</title>
              <style>
                  body { font-family: Arial, sans-serif; margin: 20px; background-color: #fefcbf; }
                  .alert { background-color: #faf089; border: 2px solid #ecc94b; padding: 20px; margin: 15px 0; border-radius: 8px; }
                  .escalation { color: #744210; }
              </style>
          </head>
          <body>
              <h1>📈 ESCALATED ALERT</h1>
              <p><strong>⏰ PERSISTENT ISSUE REQUIRES MANAGEMENT ATTENTION</strong></p>

              {{ range .Alerts }}
              <div class="alert escalation">
                  <h2>{{ .Annotations.summary }}</h2>
                  <p><strong>Escalation Level:</strong> {{ .Labels.escalation }}</p>
                  <p><strong>Original Alert:</strong> {{ .Labels.original_alert }}</p>
                  <p><strong>Description:</strong> {{ .Annotations.description }}</p>
                  <p><strong>Started:</strong> {{ .StartsAt.Format "2006-01-02 15:04:05 UTC" }}</p>
              </div>
              {{ end }}
          </body>
          </html>

  - name: 'capacity-alerts'
    email_configs:
      - to: '${ALERT_EMAIL_CAPACITY}'
        from: '${SMTP_FROM}'
        subject: '📊 CAPACITY PLANNING: {{ .GroupLabels.alertname }}'
        html: |
          <!DOCTYPE html>
          <html>
          <head>
              <meta charset="UTF-8">
              <title>Capacity Planning Alert</title>
              <style>
                  body { font-family: Arial, sans-serif; margin: 20px; background-color: #ebf8ff; }
                  .alert { background-color: #bee3f8; border: 2px solid #63b3ed; padding: 20px; margin: 15px 0; border-radius: 8px; }
                  .capacity { color: #2a4365; }
              </style>
          </head>
          <body>
              <h1>📊 CAPACITY PLANNING ALERT</h1>

              {{ range .Alerts }}
              <div class="alert capacity">
                  <h2>{{ .Annotations.summary }}</h2>
                  <p><strong>Alert Type:</strong> {{ .Labels.alert_type }}</p>
                  <p><strong>Description:</strong> {{ .Annotations.description }}</p>
                  <p><strong>Started:</strong> {{ .StartsAt.Format "2006-01-02 15:04:05 UTC" }}</p>
              </div>
              {{ end }}
          </body>
          </html>

  - name: 'grouped-alerts'
    email_configs:
      - to: '${ALERT_EMAIL_CRITICAL}'
        from: '${SMTP_FROM}'
        subject: '🔗 GROUPED ALERTS: Multiple Issues Detected'
        html: |
          <!DOCTYPE html>
          <html>
          <head>
              <meta charset="UTF-8">
              <title>Grouped Alerts</title>
              <style>
                  body { font-family: Arial, sans-serif; margin: 20px; background-color: #f7fafc; }
                  .alert { background-color: #e2e8f0; border: 2px solid #a0aec0; padding: 20px; margin: 15px 0; border-radius: 8px; }
                  .grouped { color: #2d3748; }
              </style>
          </head>
          <body>
              <h1>🔗 GROUPED ALERTS</h1>
              <p><strong>🚨 MULTIPLE CRITICAL ISSUES DETECTED</strong></p>

              {{ range .Alerts }}
              <div class="alert grouped">
                  <h2>{{ .Annotations.summary }}</h2>
                  <p><strong>Description:</strong> {{ .Annotations.description }}</p>
                  <p><strong>Started:</strong> {{ .StartsAt.Format "2006-01-02 15:04:05 UTC" }}</p>
              </div>
              {{ end }}
          </body>
          </html>

  - name: 'critical-alerts'
    email_configs:
      - to: '${ALERT_EMAIL_CRITICAL}'
        from: '${SMTP_FROM}'
        subject: '🚨 CRITICAL ALERT: {{ .GroupLabels.alertname }} - {{ .GroupLabels.cluster }}'
        html: |
          <!DOCTYPE html>
          <html>
          <head>
              <meta charset="UTF-8">
              <title>Critical Alert</title>
              <style>
                  body { font-family: Arial, sans-serif; margin: 20px; }
                  .alert { background-color: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; margin: 10px 0; border-radius: 5px; }
                  .critical { background-color: #f8d7da; border-color: #f5c6cb; color: #721c24; }
                  .resolved { background-color: #d4edda; border-color: #c3e6cb; color: #155724; }
                  .details { margin-top: 10px; }
                  .timestamp { font-size: 0.9em; color: #666; }
              </style>
          </head>
          <body>
              <h2>🚨 Critical Alert Notification</h2>
              <p><strong>Cluster:</strong> {{ .GroupLabels.cluster }}</p>
              <p><strong>Environment:</strong> {{ .GroupLabels.environment }}</p>
              <p><strong>Time:</strong> {{ .CommonAnnotations.timestamp }}</p>

              {{ range .Alerts }}
              <div class="alert {{ if eq .Status "resolved" }}resolved{{ else }}critical{{ end }}">
                  <h3>{{ .Annotations.summary }}</h3>
                  <p><strong>Status:</strong> {{ .Status }}</p>
                  <p><strong>Severity:</strong> {{ .Labels.severity }}</p>
                  <p><strong>Instance:</strong> {{ .Labels.instance }}</p>
                  <div class="details">
                      <p>{{ .Annotations.description }}</p>
                      {{ if .Annotations.runbook_url }}
                      <p><a href="{{ .Annotations.runbook_url }}">📖 Runbook</a></p>
                      {{ end }}
                  </div>
                  <p class="timestamp">
                      {{ if eq .Status "firing" }}Started: {{ .StartsAt.Format "2006-01-02 15:04:05 UTC" }}{{ end }}
                      {{ if eq .Status "resolved" }}Resolved: {{ .EndsAt.Format "2006-01-02 15:04:05 UTC" }}{{ end }}
                  </p>
              </div>
              {{ end }}

              <p><small>This is an automated alert from Heibooky Monitoring System</small></p>
          </body>
          </html>
        headers:
          X-Priority: '1'
          X-MSMail-Priority: 'High'
          Importance: 'high'
    webhook_configs:
      - url: 'http://web:8000/monitoring/webhook/'
        send_resolved: true
        http_config:
          tls_config:
            insecure_skip_verify: false

  - name: 'security-alerts'
    email_configs:
      - to: '${ALERT_EMAIL_SECURITY}'
        from: '${SMTP_FROM}'
        subject: '🔒 SECURITY ALERT: {{ .GroupLabels.alertname }} - {{ .GroupLabels.cluster }}'
        html: |
          <!DOCTYPE html>
          <html>
          <head>
              <meta charset="UTF-8">
              <title>Security Alert</title>
              <style>
                  body { font-family: Arial, sans-serif; margin: 20px; }
                  .alert { background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; margin: 10px 0; border-radius: 5px; }
                  .security { background-color: #fff3cd; border-color: #ffeaa7; color: #856404; }
                  .resolved { background-color: #d4edda; border-color: #c3e6cb; color: #155724; }
                  .details { margin-top: 10px; }
                  .timestamp { font-size: 0.9em; color: #666; }
              </style>
          </head>
          <body>
              <h2>🔒 Security Alert Notification</h2>
              <p><strong>Cluster:</strong> {{ .GroupLabels.cluster }}</p>
              <p><strong>Environment:</strong> {{ .GroupLabels.environment }}</p>
              <p><strong>Time:</strong> {{ .CommonAnnotations.timestamp }}</p>

              {{ range .Alerts }}
              <div class="alert {{ if eq .Status "resolved" }}resolved{{ else }}security{{ end }}">
                  <h3>{{ .Annotations.summary }}</h3>
                  <p><strong>Status:</strong> {{ .Status }}</p>
                  <p><strong>Category:</strong> {{ .Labels.category }}</p>
                  <p><strong>Instance:</strong> {{ .Labels.instance }}</p>
                  <div class="details">
                      <p>{{ .Annotations.description }}</p>
                      {{ if .Annotations.remediation }}
                      <p><strong>Remediation:</strong> {{ .Annotations.remediation }}</p>
                      {{ end }}
                  </div>
                  <p class="timestamp">
                      {{ if eq .Status "firing" }}Started: {{ .StartsAt.Format "2006-01-02 15:04:05 UTC" }}{{ end }}
                      {{ if eq .Status "resolved" }}Resolved: {{ .EndsAt.Format "2006-01-02 15:04:05 UTC" }}{{ end }}
                  </p>
              </div>
              {{ end }}

              <p><small>This is an automated security alert from Heibooky Monitoring System</small></p>
          </body>
          </html>
        headers:
          X-Priority: '1'
          X-MSMail-Priority: 'High'
          Importance: 'high'
    webhook_configs:
      - url: 'http://web:8000/monitoring/webhook/'
        send_resolved: true

  - name: 'warning-alerts'
    email_configs:
      - to: '${ALERT_EMAIL_WARNING}'
        from: '${SMTP_FROM}'
        subject: '⚠️ WARNING: {{ .GroupLabels.alertname }} - {{ .GroupLabels.cluster }}'
        html: |
          <!DOCTYPE html>
          <html>
          <head>
              <meta charset="UTF-8">
              <title>Warning Alert</title>
              <style>
                  body { font-family: Arial, sans-serif; margin: 20px; }
                  .alert { background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; margin: 10px 0; border-radius: 5px; }
                  .warning { background-color: #fff3cd; border-color: #ffeaa7; color: #856404; }
                  .resolved { background-color: #d4edda; border-color: #c3e6cb; color: #155724; }
                  .details { margin-top: 10px; }
                  .timestamp { font-size: 0.9em; color: #666; }
              </style>
          </head>
          <body>
              <h2>⚠️ Warning Alert Notification</h2>
              <p><strong>Cluster:</strong> {{ .GroupLabels.cluster }}</p>
              <p><strong>Environment:</strong> {{ .GroupLabels.environment }}</p>

              {{ range .Alerts }}
              <div class="alert {{ if eq .Status "resolved" }}resolved{{ else }}warning{{ end }}">
                  <h3>{{ .Annotations.summary }}</h3>
                  <p><strong>Status:</strong> {{ .Status }}</p>
                  <p><strong>Severity:</strong> {{ .Labels.severity }}</p>
                  <p><strong>Instance:</strong> {{ .Labels.instance }}</p>
                  <div class="details">
                      <p>{{ .Annotations.description }}</p>
                  </div>
                  <p class="timestamp">
                      {{ if eq .Status "firing" }}Started: {{ .StartsAt.Format "2006-01-02 15:04:05 UTC" }}{{ end }}
                      {{ if eq .Status "resolved" }}Resolved: {{ .EndsAt.Format "2006-01-02 15:04:05 UTC" }}{{ end }}
                  </p>
              </div>
              {{ end }}

              <p><small>This is an automated warning from Heibooky Monitoring System</small></p>
          </body>
          </html>

  - name: 'info-alerts'
    email_configs:
      - to: '${ALERT_EMAIL_INFO}'
        from: '${SMTP_FROM}'
        subject: 'ℹ️ INFO: Daily Monitoring Digest - {{ .GroupLabels.cluster }}'
        html: |
          <!DOCTYPE html>
          <html>
          <head>
              <meta charset="UTF-8">
              <title>Info Alert</title>
              <style>
                  body { font-family: Arial, sans-serif; margin: 20px; }
                  .alert { background-color: #d1ecf1; border: 1px solid #bee5eb; padding: 15px; margin: 10px 0; border-radius: 5px; }
                  .info { background-color: #d1ecf1; border-color: #bee5eb; color: #0c5460; }
                  .details { margin-top: 10px; }
                  .timestamp { font-size: 0.9em; color: #666; }
              </style>
          </head>
          <body>
              <h2>ℹ️ Information Alert</h2>
              <p><strong>Cluster:</strong> {{ .GroupLabels.cluster }}</p>
              <p><strong>Environment:</strong> {{ .GroupLabels.environment }}</p>

              {{ range .Alerts }}
              <div class="alert info">
                  <h3>{{ .Annotations.summary }}</h3>
                  <p><strong>Status:</strong> {{ .Status }}</p>
                  <p><strong>Instance:</strong> {{ .Labels.instance }}</p>
                  <div class="details">
                      <p>{{ .Annotations.description }}</p>
                  </div>
                  <p class="timestamp">Time: {{ .StartsAt.Format "2006-01-02 15:04:05 UTC" }}</p>
              </div>
              {{ end }}

              <p><small>This is an automated info alert from Heibooky Monitoring System</small></p>
          </body>
          </html>

inhibit_rules:
  # SLO-based inhibition - SLO alerts take precedence
  - source_match:
      slo: 'availability'
      severity: 'critical'
    target_match:
      severity: 'critical'
    target_match_re:
      alertname: 'HighErrorRate|DjangoAppDown'
    equal: ['instance']

  - source_match:
      slo: 'latency'
      severity: 'critical'
    target_match:
      severity: 'critical'
    target_match_re:
      alertname: 'HighRequestLatency'
    equal: ['instance']

  # Business impact alerts take precedence over technical alerts
  - source_match:
      impact: 'business_critical'
    target_match:
      severity: 'warning'
    equal: ['instance']

  - source_match:
      impact: 'revenue'
    target_match:
      severity: 'warning'
    equal: ['instance']

  # Multi-window alerts inhibit single-window alerts
  - source_match:
      alert_type: 'multi_window'
    target_match_re:
      alertname: 'SLO.*BurnRate.*'
    equal: ['slo', 'instance']

  # Grouped alerts inhibit individual alerts
  - source_match:
      alert_type: 'grouped'
    target_match:
      severity: 'critical'
    equal: ['cluster']

  # Escalation alerts don't inhibit original alerts (continue: true handles this)

  # Standard severity-based inhibition
  - source_match:
      severity: 'critical'
    target_match:
      severity: 'warning'
    equal: ['alertname', 'instance', 'job']

  # Inhibit info alerts when warning or critical alerts are firing
  - source_match:
      severity: 'warning'
    target_match:
      severity: 'info'
    equal: ['alertname', 'instance', 'job']

  - source_match:
      severity: 'critical'
    target_match:
      severity: 'info'
    equal: ['alertname', 'instance', 'job']

  # Dependency-aware inhibition
  - source_match:
      alertname: 'DjangoAppDown'
    target_match_re:
      alertname: '.*High.*|.*Slow.*|.*Error.*'
    equal: ['instance']

  # Inhibit downstream alerts when upstream service is down
  - source_match:
      alertname: 'RedisDown'
    target_match_re:
      alertname: '.*Cache.*|.*Session.*'
    equal: ['cluster']

  # Inhibit duplicate alerts from the same instance
  - source_match:
      alertname: 'InstanceDown'
    target_match_re:
      alertname: '.*'
    equal: ['instance']

  # Capacity alerts don't inhibit operational alerts
  - source_match:
      alert_type: 'capacity'
    target_match:
      alert_type: 'capacity'
    equal: ['alertname', 'instance']
