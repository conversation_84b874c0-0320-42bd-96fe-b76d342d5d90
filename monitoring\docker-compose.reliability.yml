# Docker Compose High Availability and Reliability Override
# Use with: docker-compose -f docker-compose.yml -f monitoring/docker-compose.reliability.yml up -d

version: '3.8'

services:
  # Prometheus with enhanced reliability
  prometheus:
    restart: unless-stopped
    deploy:
      restart_policy:
        condition: on-failure
        delay: 30s
        max_attempts: 5
        window: 120s
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--storage.tsdb.retention.time=${PROMETHEUS_RETENTION_TIME:-15d}'
      - '--storage.tsdb.retention.size=${PROMETHEUS_RETENTION_SIZE:-10GB}'
      - '--storage.tsdb.wal-compression'
      - '--storage.tsdb.min-block-duration=2h'
      - '--storage.tsdb.max-block-duration=36h'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--web.enable-lifecycle'
      - '--web.enable-admin-api=${PROMETHEUS_WEB_ENABLE_ADMIN_API:-false}'
      - '--web.max-connections=${PROMETHEUS_WEB_MAX_CONNECTIONS:-512}'
      - '--query.timeout=${PROMETHEUS_QUERY_TIMEOUT:-2m}'
      - '--query.max-concurrency=${PROMETHEUS_QUERY_MAX_CONCURRENCY:-20}'
      - '--query.max-samples=50000000'
      - '--query.lookback-delta=5m'
      - '--log.level=info'
      - '--log.format=json'
      # Reliability features
      - '--storage.tsdb.no-lockfile'
      - '--storage.tsdb.head-chunks-write-queue-size=0'
      - '--web.enable-remote-shutdown=false'
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:9090/-/healthy"]
      interval: 15s
      timeout: 10s
      retries: 5
      start_period: 60s
    volumes:
      - prometheus_data:/prometheus
      - ./monitoring/prometheus:/etc/prometheus:ro
      - ./monitoring/reliability/prometheus-backup:/backup
    environment:
      - PROMETHEUS_EXTERNAL_LABELS_REPLICA=${HOSTNAME:-prometheus-1}
      - PROMETHEUS_EXTERNAL_LABELS_CLUSTER=${CLUSTER_NAME:-heibooky-production}
    logging:
      driver: "json-file"
      options:
        max-size: "50m"
        max-file: "5"
        labels: "service,version"
    labels:
      - "monitoring.service=prometheus"
      - "monitoring.backup=enabled"
      - "monitoring.ha=primary"

  # Grafana with enhanced reliability
  grafana:
    restart: unless-stopped
    deploy:
      restart_policy:
        condition: on-failure
        delay: 30s
        max_attempts: 5
        window: 120s
    environment:
      # Enhanced reliability settings
      - GF_SERVER_ENABLE_GZIP=true
      - GF_SERVER_ENFORCE_DOMAIN=false
      
      # Database reliability
      - GF_DATABASE_TYPE=${GF_DATABASE_TYPE:-sqlite3}
      - GF_DATABASE_HOST=${GF_DATABASE_HOST:-}
      - GF_DATABASE_NAME=${GF_DATABASE_NAME:-grafana}
      - GF_DATABASE_USER=${GF_DATABASE_USER:-}
      - GF_DATABASE_PASSWORD=${GF_DATABASE_PASSWORD:-}
      - GF_DATABASE_SSL_MODE=${GF_DATABASE_SSL_MODE:-disable}
      - GF_DATABASE_MAX_IDLE_CONN=${GF_DATABASE_MAX_IDLE_CONN:-25}
      - GF_DATABASE_MAX_OPEN_CONN=${GF_DATABASE_MAX_OPEN_CONN:-300}
      - GF_DATABASE_CONN_MAX_LIFETIME=${GF_DATABASE_CONN_MAX_LIFETIME:-14400}
      - GF_DATABASE_QUERY_RETRIES=${GF_DATABASE_QUERY_RETRIES:-3}
      - GF_DATABASE_QUERY_TIMEOUT=${GF_DATABASE_QUERY_TIMEOUT:-30s}
      
      # Session reliability (Redis for clustering)
      - GF_SESSION_PROVIDER=${GF_SESSION_PROVIDER:-redis}
      - GF_SESSION_PROVIDER_CONFIG=${GF_SESSION_PROVIDER_CONFIG:-addr=redis:6379,pool_size=100,db=grafana}
      - GF_SESSION_COOKIE_SECURE=${GF_SESSION_COOKIE_SECURE:-true}
      - GF_SESSION_COOKIE_SAMESITE=${GF_SESSION_COOKIE_SAMESITE:-strict}
      
      # Error handling
      - GF_LOG_MODE=console,file
      - GF_LOG_LEVEL=${GF_LOG_LEVEL:-info}
      - GF_LOG_FILTERS=${GF_LOG_FILTERS:-rendering:debug}
      
      # Alerting reliability
      - GF_ALERTING_ENABLED=true
      - GF_ALERTING_EXECUTE_ALERTS=true
      - GF_ALERTING_ERROR_OR_TIMEOUT=alerting
      - GF_ALERTING_NODATA_OR_NULLVALUES=no_data
      - GF_ALERTING_CONCURRENT_RENDER_LIMIT=${GF_ALERTING_CONCURRENT_RENDER_LIMIT:-5}
      - GF_ALERTING_EVALUATION_TIMEOUT_SECONDS=${GF_ALERTING_EVALUATION_TIMEOUT_SECONDS:-30}
      - GF_ALERTING_NOTIFICATION_TIMEOUT_SECONDS=${GF_ALERTING_NOTIFICATION_TIMEOUT_SECONDS:-30}
      - GF_ALERTING_MAX_ATTEMPTS=${GF_ALERTING_MAX_ATTEMPTS:-3}
      
      # Unified alerting reliability
      - GF_UNIFIED_ALERTING_ENABLED=true
      - GF_UNIFIED_ALERTING_HA_LISTEN_ADDRESS=${POD_IP:-0.0.0.0}:9094
      - GF_UNIFIED_ALERTING_HA_ADVERTISE_ADDRESS=${POD_IP:-grafana}:9094
      - GF_UNIFIED_ALERTING_HA_PEERS=${GF_UNIFIED_ALERTING_HA_PEERS:-}
      - GF_UNIFIED_ALERTING_HA_PEER_TIMEOUT=${GF_UNIFIED_ALERTING_HA_PEER_TIMEOUT:-15s}
      - GF_UNIFIED_ALERTING_HA_GOSSIP_INTERVAL=${GF_UNIFIED_ALERTING_HA_GOSSIP_INTERVAL:-200ms}
      - GF_UNIFIED_ALERTING_HA_PUSH_PULL_INTERVAL=${GF_UNIFIED_ALERTING_HA_PUSH_PULL_INTERVAL:-60s}
      - GF_UNIFIED_ALERTING_MAX_ATTEMPTS=${GF_UNIFIED_ALERTING_MAX_ATTEMPTS:-3}
      - GF_UNIFIED_ALERTING_MIN_INTERVAL=${GF_UNIFIED_ALERTING_MIN_INTERVAL:-10s}
      
      # Feature toggles for reliability
      - GF_FEATURE_TOGGLES_ENABLE=${GF_FEATURE_TOGGLES_ENABLE:-ngalert,publicDashboards,autoMigrateOldAlerting}
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:3000/api/health || exit 1"]
      interval: 15s
      timeout: 10s
      retries: 5
      start_period: 60s
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana:/etc/grafana:ro
      - ./monitoring/reliability/grafana-backup:/backup
    depends_on:
      prometheus:
        condition: service_healthy
      redis:
        condition: service_healthy
    logging:
      driver: "json-file"
      options:
        max-size: "50m"
        max-file: "5"
        labels: "service,version"
    labels:
      - "monitoring.service=grafana"
      - "monitoring.backup=enabled"
      - "monitoring.ha=primary"

  # Loki with enhanced reliability
  loki:
    restart: unless-stopped
    deploy:
      restart_policy:
        condition: on-failure
        delay: 30s
        max_attempts: 5
        window: 120s
    command:
      - '-config.file=/etc/loki/local-config.yaml'
      - '-log.level=info'
      - '-log.format=json'
      # Reliability flags
      - '-server.graceful-shutdown-timeout=30s'
      - '-server.http-idle-timeout=120s'
      - '-server.http-read-timeout=30s'
      - '-server.http-write-timeout=30s'
    environment:
      # Enhanced reliability settings
      - LOKI_INGESTION_RATE_MB=${LOKI_INGESTION_RATE_MB:-4}
      - LOKI_INGESTION_BURST_SIZE_MB=${LOKI_INGESTION_BURST_SIZE_MB:-6}
      - LOKI_MAX_QUERY_PARALLELISM=${LOKI_MAX_QUERY_PARALLELISM:-32}
      - LOKI_QUERY_TIMEOUT=${LOKI_QUERY_TIMEOUT:-1m}
      - LOKI_MAX_ENTRIES_LIMIT=${LOKI_MAX_ENTRIES_LIMIT:-5000}
      - LOKI_SPLIT_QUERIES_BY_INTERVAL=${LOKI_SPLIT_QUERIES_BY_INTERVAL:-30m}
      - LOKI_CACHE_RESULTS=${LOKI_CACHE_RESULTS:-true}
      - LOKI_RESULTS_CACHE_MAX_SIZE_MB=${LOKI_RESULTS_CACHE_MAX_SIZE_MB:-500}
      - LOKI_RESULTS_CACHE_TTL=${LOKI_RESULTS_CACHE_TTL:-1h}
      - LOKI_CHUNK_IDLE_PERIOD=${LOKI_CHUNK_IDLE_PERIOD:-1h}
      - LOKI_MAX_CHUNK_AGE=${LOKI_MAX_CHUNK_AGE:-1h}
      - LOKI_CHUNK_TARGET_SIZE=${LOKI_CHUNK_TARGET_SIZE:-1048576}
      - LOKI_COMPACTION_INTERVAL=${LOKI_COMPACTION_INTERVAL:-10m}
      - LOKI_RETENTION_PERIOD=${LOKI_RETENTION_PERIOD:-336h}
    healthcheck:
      test: ["CMD-SHELL", "wget --no-verbose --tries=1 --spider http://localhost:3100/ready || exit 1"]
      interval: 15s
      timeout: 10s
      retries: 5
      start_period: 60s
    volumes:
      - loki_data:/loki
      - ./monitoring/loki:/etc/loki:ro
      - ./monitoring/reliability/loki-backup:/backup
    logging:
      driver: "json-file"
      options:
        max-size: "50m"
        max-file: "5"
        labels: "service,version"
    labels:
      - "monitoring.service=loki"
      - "monitoring.backup=enabled"
      - "monitoring.ha=primary"

  # Alertmanager with enhanced reliability
  alertmanager:
    restart: unless-stopped
    deploy:
      restart_policy:
        condition: on-failure
        delay: 30s
        max_attempts: 5
        window: 120s
    command:
      - '--config.file=/etc/alertmanager/alertmanager.yml'
      - '--storage.path=/alertmanager'
      - '--web.external-url=${ALERTMANAGER_WEB_EXTERNAL_URL:-http://alertmanager:9093}'
      - '--web.route-prefix=${ALERTMANAGER_WEB_ROUTE_PREFIX:-/}'
      - '--cluster.listen-address=${ALERTMANAGER_CLUSTER_LISTEN_ADDRESS:-0.0.0.0:9094}'
      - '--cluster.peer=${ALERTMANAGER_CLUSTER_PEER:-}'
      - '--cluster.peer-timeout=${AM_CLUSTER_PEER_TIMEOUT:-15s}'
      - '--cluster.gossip-interval=${AM_CLUSTER_GOSSIP_INTERVAL:-200ms}'
      - '--cluster.pushpull-interval=${AM_CLUSTER_PUSH_PULL_INTERVAL:-60s}'
      - '--data.retention=${AM_DATA_RETENTION:-120h}'
      - '--log.level=info'
      - '--log.format=json'
      # Reliability flags
      - '--web.timeout=30s'
      - '--cluster.tcp-timeout=10s'
      - '--cluster.probe-timeout=500ms'
      - '--cluster.probe-interval=1s'
    healthcheck:
      test: ["CMD-SHELL", "wget --no-verbose --tries=1 --spider http://localhost:9093/-/healthy || exit 1"]
      interval: 15s
      timeout: 10s
      retries: 5
      start_period: 60s
    volumes:
      - alertmanager_data:/alertmanager
      - ./monitoring/alertmanager:/etc/alertmanager:ro
      - ./monitoring/reliability/alertmanager-backup:/backup
    depends_on:
      prometheus:
        condition: service_healthy
    logging:
      driver: "json-file"
      options:
        max-size: "50m"
        max-file: "5"
        labels: "service,version"
    labels:
      - "monitoring.service=alertmanager"
      - "monitoring.backup=enabled"
      - "monitoring.ha=primary"

  # Enhanced Redis for session clustering and caching
  redis:
    restart: unless-stopped
    deploy:
      restart_policy:
        condition: on-failure
        delay: 10s
        max_attempts: 5
        window: 60s
    command: >
      sh -c 'redis-server /etc/redis/redis.conf \
        --aclfile /etc/redis/users.acl \
        --save 900 1 \
        --save 300 10 \
        --save 60 10000 \
        --stop-writes-on-bgsave-error yes \
        --rdbcompression yes \
        --rdbchecksum yes \
        --maxmemory-policy allkeys-lru'
    healthcheck:
      test: ["CMD-SHELL", "redis-cli --no-auth-warning --user monitoring -a $(cat /run/secrets/redis_password) ping"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    logging:
      driver: "json-file"
      options:
        max-size: "50m"
        max-file: "5"
        labels: "service,version"
    labels:
      - "monitoring.service=redis"
      - "monitoring.backup=enabled"
      - "monitoring.ha=primary"

  # Enhanced exporters with reliability
  redis-exporter:
    restart: unless-stopped
    deploy:
      restart_policy:
        condition: on-failure
        delay: 10s
        max_attempts: 3
        window: 60s
    healthcheck:
      test: ["CMD-SHELL", "wget --no-verbose --tries=1 --spider http://localhost:9121/metrics || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  node-exporter:
    restart: unless-stopped
    deploy:
      restart_policy:
        condition: on-failure
        delay: 10s
        max_attempts: 3
        window: 60s
    healthcheck:
      test: ["CMD-SHELL", "wget --no-verbose --tries=1 --spider http://localhost:9100/metrics || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  cadvisor:
    restart: unless-stopped
    deploy:
      restart_policy:
        condition: on-failure
        delay: 10s
        max_attempts: 3
        window: 60s
    healthcheck:
      test: ["CMD-SHELL", "wget --no-verbose --tries=1 --spider http://localhost:8080/healthz || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  promtail:
    restart: unless-stopped
    deploy:
      restart_policy:
        condition: on-failure
        delay: 10s
        max_attempts: 3
        window: 60s
    healthcheck:
      test: ["CMD-SHELL", "wget --no-verbose --tries=1 --spider http://localhost:9080/ready || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  # Health check and recovery service
  monitoring-healthcheck:
    image: alpine:latest
    container_name: monitoring-healthcheck
    restart: unless-stopped
    command: >
      sh -c "
        apk add --no-cache curl wget jq &&
        while true; do
          /scripts/health-monitor.sh
          sleep 60
        done
      "
    volumes:
      - ./scripts:/scripts:ro
      - /var/run/docker.sock:/var/run/docker.sock:ro
    networks:
      - monitoring
    depends_on:
      - prometheus
      - grafana
      - loki
      - alertmanager
    environment:
      - MONITORING_SERVICES=prometheus,grafana,loki,alertmanager
      - HEALTH_CHECK_INTERVAL=60
      - RECOVERY_ENABLED=true
    labels:
      - "monitoring.service=healthcheck"
      - "monitoring.role=recovery"

# Enhanced volumes with backup capabilities
volumes:
  prometheus_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${PROMETHEUS_DATA_PATH:-./monitoring/data/prometheus}
    labels:
      - "backup.enabled=true"
      - "backup.schedule=0 2 * * *"
      - "backup.retention=30d"
  
  grafana_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${GRAFANA_DATA_PATH:-./monitoring/data/grafana}
    labels:
      - "backup.enabled=true"
      - "backup.schedule=0 3 * * *"
      - "backup.retention=30d"
  
  alertmanager_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${ALERTMANAGER_DATA_PATH:-./monitoring/data/alertmanager}
    labels:
      - "backup.enabled=true"
      - "backup.schedule=0 5 * * *"
      - "backup.retention=30d"
  
  loki_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${LOKI_DATA_PATH:-./monitoring/data/loki}
    labels:
      - "backup.enabled=true"
      - "backup.schedule=0 4 * * *"
      - "backup.retention=90d"

# Enhanced networks with reliability features
networks:
  monitoring:
    driver: bridge
    driver_opts:
      com.docker.network.bridge.name: br-monitoring
      com.docker.network.driver.mtu: 1500
      com.docker.network.bridge.enable_icc: "true"
      com.docker.network.bridge.enable_ip_masquerade: "true"
    ipam:
      config:
        - subnet: **********/16
          gateway: **********
    labels:
      - "network.monitoring=true"
      - "network.ha=enabled"

  backend:
    driver: bridge
    driver_opts:
      com.docker.network.bridge.name: br-backend
      com.docker.network.driver.mtu: 1500
      com.docker.network.bridge.enable_icc: "true"
      com.docker.network.bridge.enable_ip_masquerade: "true"
    ipam:
      config:
        - subnet: **********/16
          gateway: **********
    labels:
      - "network.backend=true"
      - "network.ha=enabled"
