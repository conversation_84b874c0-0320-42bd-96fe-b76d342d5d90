# Monitoring Stack Security Guide

This document outlines the security measures implemented in the Heibooky monitoring stack and provides guidelines for maintaining a secure monitoring environment.

## Table of Contents

1. [Security Overview](#security-overview)
2. [Authentication and Authorization](#authentication-and-authorization)
3. [Network Security](#network-security)
4. [Container Security](#container-security)
5. [Data Protection](#data-protection)
6. [Monitoring Security](#monitoring-security)
7. [Incident Response](#incident-response)
8. [Security Checklist](#security-checklist)

## Security Overview

The monitoring stack implements defense-in-depth security principles:

- **Network Isolation**: Separate networks for monitoring and backend services
- **Container Hardening**: Security-focused container configurations
- **Access Controls**: Role-based access with strong authentication
- **Data Encryption**: TLS encryption for data in transit
- **Audit Logging**: Comprehensive security event logging
- **Regular Updates**: Automated security updates and vulnerability scanning

## Authentication and Authorization

### Grafana Security

#### Strong Password Policy
- Minimum 12 characters
- Must include uppercase, lowercase, numbers, and symbols
- Password expiration: 90 days
- Account lockout after 5 failed attempts

#### Session Management
- Secure cookies with `HttpOnly` and `Secure` flags
- Session timeout: 2 hours idle, 24 hours maximum
- CSRF protection enabled
- SameSite cookie policy: `Strict`

#### User Management
- User registration disabled
- Organization creation disabled
- Default role: `Viewer`
- Admin privileges require explicit assignment

#### Configuration
```ini
[security]
admin_password = ${GF_SECURITY_ADMIN_PASSWORD}
secret_key = ${GF_SECURITY_SECRET_KEY}
cookie_secure = true
cookie_samesite = strict
password_min_length = 12
login_maximum_inactive_lifetime_duration = 2h
login_maximum_lifetime_duration = 24h
```

### Prometheus Security

#### Access Control
- Internal network access only
- No external authentication (protected by network isolation)
- Admin API disabled in production
- Lifecycle API disabled in production

#### Query Security
- Query timeout: 2 minutes
- Maximum concurrent queries: 20
- Query logging enabled for audit

### Alertmanager Security

#### Webhook Security
- HTTPS-only webhook endpoints
- Webhook authentication tokens
- Rate limiting on webhook endpoints
- Request validation and sanitization

## Network Security

### Network Segmentation

```yaml
networks:
  monitoring:
    subnet: 172.21.0.0/16
    isolation: true
  backend:
    subnet: 172.20.0.0/16
```

### Firewall Rules

#### Ingress Rules
- Grafana (3000/tcp): Backend network only
- Prometheus (9090/tcp): Monitoring network only
- Alertmanager (9093/tcp): Monitoring network only
- Loki (3100/tcp): Monitoring network only

#### Egress Rules
- SMTP (25, 587/tcp): Internet for email alerts
- HTTPS (443/tcp): Internet for updates and integrations
- DNS (53/udp): Internal DNS resolution

### TLS Configuration

#### Grafana HTTPS
```ini
[server]
protocol = https
cert_file = /etc/ssl/certs/grafana.crt
cert_key = /etc/ssl/private/grafana.key
```

#### Certificate Management
- Use Let's Encrypt or internal CA
- Automatic certificate renewal
- Strong cipher suites only (TLS 1.2+)
- HSTS headers enabled

## Container Security

### Security Options

All containers implement:
```yaml
security_opt:
  - no-new-privileges:true
cap_drop:
  - ALL
cap_add:
  - CHOWN
  - SETGID
  - SETUID
read_only: true
```

### User Privileges
- Grafana: `472:472` (grafana user)
- Prometheus: `nobody:nobody`
- Alertmanager: `nobody:nobody`
- Loki: `10001:10001`

### Resource Limits
```yaml
mem_limit: 512m
cpus: 0.5
tmpfs:
  - /tmp:noexec,nosuid,size=100m
```

### Image Security
- Use official images only
- Pin specific versions (no `latest` tags)
- Regular vulnerability scanning
- Minimal base images (Alpine Linux)

## Data Protection

### Data at Rest
- Encrypted storage volumes
- Secure file permissions (0600 for secrets)
- Regular backup encryption
- Key rotation every 90 days

### Data in Transit
- TLS 1.2+ for all external communications
- Internal network encryption for sensitive data
- Certificate validation enabled
- Strong cipher suites only

### Data Retention
- Metrics: 15 days
- Logs: 30 days
- Alerts: 90 days
- Audit logs: 365 days

### Secrets Management
```yaml
environment:
  - GRAFANA_ADMIN_PASSWORD=${GF_SECURITY_ADMIN_PASSWORD}
  - SMTP_PASSWORD=${SMTP_PASSWORD}
secrets:
  - grafana_admin_password
  - smtp_credentials
```

## Monitoring Security

### Security Metrics

Track these security-related metrics:
- Failed login attempts
- Suspicious query patterns
- Configuration changes
- Certificate expiry dates
- Vulnerability scan results

### Security Alerts

#### Critical Alerts
- Multiple failed login attempts (>5 in 5 minutes)
- Unauthorized configuration changes
- Certificate expiry (30 days warning)
- Security scan failures

#### Alert Configuration
```yaml
- alert: SecurityBreach
  expr: rate(failed_login_attempts[5m]) > 5
  for: 0m
  labels:
    severity: critical
    category: security
  annotations:
    summary: "Multiple failed login attempts detected"
    description: "{{ $value }} failed login attempts in the last 5 minutes"
```

### Audit Logging

#### Events to Log
- Authentication events (success/failure)
- Authorization failures
- Configuration changes
- Admin actions
- Query execution (for suspicious patterns)

#### Log Format
```json
{
  "timestamp": "2024-01-01T12:00:00Z",
  "event_type": "authentication_failure",
  "user": "admin",
  "source_ip": "*************",
  "user_agent": "Mozilla/5.0...",
  "details": {
    "reason": "invalid_password",
    "attempt_count": 3
  }
}
```

## Incident Response

### Security Incident Procedures

1. **Detection**: Automated alerts and monitoring
2. **Assessment**: Determine scope and impact
3. **Containment**: Isolate affected systems
4. **Eradication**: Remove threats and vulnerabilities
5. **Recovery**: Restore normal operations
6. **Lessons Learned**: Update procedures and controls

### Emergency Contacts
- Security Team: <EMAIL>
- On-call Engineer: <EMAIL>
- Management: <EMAIL>

### Incident Response Playbooks
- [Authentication Breach](./playbooks/auth-breach.md)
- [Data Exfiltration](./playbooks/data-exfiltration.md)
- [System Compromise](./playbooks/system-compromise.md)

## Security Checklist

### Daily
- [ ] Review security alerts
- [ ] Check failed login attempts
- [ ] Verify backup completion
- [ ] Monitor resource usage

### Weekly
- [ ] Review access logs
- [ ] Update security patches
- [ ] Vulnerability scan results
- [ ] Certificate expiry check

### Monthly
- [ ] Access review (user permissions)
- [ ] Security configuration audit
- [ ] Incident response drill
- [ ] Security metrics review

### Quarterly
- [ ] Penetration testing
- [ ] Security policy review
- [ ] Disaster recovery test
- [ ] Security training update

## Security Configuration Files

### Environment Variables
```bash
# Copy and customize monitoring/.env.template
cp monitoring/.env.template monitoring/.env
```

### Security Policies
- [Network Security Policy](./policies/network-security.md)
- [Access Control Policy](./policies/access-control.md)
- [Data Protection Policy](./policies/data-protection.md)

## Compliance

### Standards Compliance
- **CIS Controls**: Container and network security
- **NIST Cybersecurity Framework**: Risk management
- **GDPR**: Data privacy and protection
- **SOC 2**: Security controls and monitoring

### Audit Requirements
- Security event logging
- Access control documentation
- Incident response procedures
- Regular security assessments

## Security Tools Integration

### Vulnerability Scanning
```bash
# Container vulnerability scanning
docker run --rm -v /var/run/docker.sock:/var/run/docker.sock \
  aquasec/trivy image grafana/grafana:10.1.0
```

### Security Monitoring
- SIEM integration via Loki
- Intrusion detection alerts
- Behavioral analysis
- Threat intelligence feeds

## Contact Information

For security-related questions or incidents:
- **Security Team**: <EMAIL>
- **Emergency**: ******-SECURITY
- **Documentation**: https://docs.heibooky.com/security

---

**Last Updated**: 2024-01-01  
**Next Review**: 2024-04-01  
**Document Owner**: Security Team
