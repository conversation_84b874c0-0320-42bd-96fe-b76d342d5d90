[server]
http_port = 3000
domain = grafana.heibooky.com
root_url = %(protocol)s://%(domain)s:%(http_port)s/
protocol = https
enforce_domain = true
cert_file = /etc/ssl/certs/grafana.crt
cert_key = /etc/ssl/private/grafana.key
enable_gzip = true

[security]
admin_user = admin
admin_password = ${GF_SECURITY_ADMIN_PASSWORD}
secret_key = ${GF_SECURITY_SECRET_KEY}
login_remember_days = 7
cookie_secure = true
cookie_samesite = strict
strict_transport_security = true
strict_transport_security_max_age_seconds = 86400
strict_transport_security_preload = true
strict_transport_security_subdomains = true
content_type_protection = true
x_content_type_options = nosniff
x_xss_protection = true
disable_gravatar = true
disable_brute_force_login_protection = false
login_maximum_inactive_lifetime_duration = 7d
login_maximum_lifetime_duration = 30d
password_min_length = 12
password_complexity = true

[users]
allow_sign_up = false
allow_org_create = false
default_role = Viewer
auto_assign_org = true
auto_assign_org_id = 1
auto_assign_org_role = Viewer
verify_email_enabled = false
login_hint = email or username
password_hint = your password

[auth]
disable_login_form = false
disable_signout_menu = false
signout_redirect_url =
oauth_auto_login = false
oauth_state_cookie_max_age = 600

[auth.anonymous]
enabled = false

[dashboards]
default_home_dashboard_path = /etc/grafana/provisioning/dashboards/heibooky-overview.json

[alerting]
enabled = true
execute_alerts = true

[unified_alerting]
enabled = true
admin_config_poll_interval = 60s
alertmanager_config_poll_interval = 60s
ha_listen_address = "${POD_IP}:9094"
ha_advertise_address = "${POD_IP}:9094"
ha_peers = ""
ha_peer_timeout = 15s
ha_gossip_interval = 200ms
ha_push_pull_interval = 60s
ha_redis_addr = ""

[log]
mode = console file
level = info
filters = rendering:debug

[paths]
data = /var/lib/grafana
logs = /var/log/grafana
plugins = /var/lib/grafana/plugins
provisioning = /etc/grafana/provisioning
temp_data_lifetime = 24h

[analytics]
reporting_enabled = false
check_for_updates = false
google_analytics_ua_id = ""
google_tag_manager_id = ""

[feature_toggles]
enable = ngalert

[plugins]
enable_alpha = false
app_tls_skip_verify_insecure = false
allow_loading_unsigned_plugins = ""

[live]
max_connections = 100

[enterprise]
license_path = ""

[panels]
disable_sanitize_html = false

[query_history]
enabled = true

[expressions]
enabled = true

[explore]
enabled = true

[help]
enabled = true

[profile]
enabled = true

[news]
news_feed_enabled = false

[quota]
enabled = false

[remote_cache]
type = database

[dataproxy]
logging = false
timeout = 30
dialTimeout = 10
keep_alive_seconds = 30

[snapshots]
external_enabled = false
external_snapshot_url = ""
external_snapshot_name = ""
snapshot_remove_expired = true

[external_image_storage]
provider = ""

[rendering]
server_url = ""
callback_url = ""
concurrent_render_request_limit = 30

[smtp]
enabled = false
host = localhost:587
user = ""
password = ""
cert_file = ""
key_file = ""
skip_verify = false
from_address = <EMAIL>
from_name = Grafana
ehlo_identity = dashboard.example.com
startTLS_policy = ""

[emails]
welcome_email_on_sign_up = false
templates_pattern = emails/*.html

[database]
url = ""
type = sqlite3
host = 127.0.0.1:3306
name = grafana
user = root
password = ""
ssl_mode = disable
ca_cert_path = ""
client_key_path = ""
client_cert_path = ""
server_cert_name = ""
path = grafana.db
max_idle_conn = 10
max_open_conn = 100
conn_max_lifetime = 14400
log_queries = false
cache_mode = shared
wal = true
query_retries = 3
query_timeout = 30s

[session]
provider = file
provider_config = sessions
cookie_name = grafana_sess
cookie_secure = true
session_life_time = 86400
gc_interval_time = 86400

# Performance optimizations
[caching]
enabled = true

[dataproxy]
logging = false
timeout = 30
dial_timeout = 10
keep_alive_seconds = 30
max_idle_connections = 100
max_idle_connections_per_host = 10
idle_conn_timeout = 90
tls_handshake_timeout = 10
expect_continue_timeout = 1

[query_history]
enabled = true
max_query_history_length = 1000

[expressions]
enabled = true

[explore]
enabled = true

[rendering]
server_url = ""
callback_url = ""
concurrent_render_request_limit = 30
rendering_timeout = 20s
rendering_ignore_https_errors = false

[alerting]
enabled = true
execute_alerts = true
error_or_timeout = alerting
nodata_or_nullvalues = no_data
concurrent_render_limit = 5
evaluation_timeout_seconds = 30
notification_timeout_seconds = 30
max_attempts = 3

[unified_alerting]
enabled = true
admin_config_poll_interval = 60s
alertmanager_config_poll_interval = 60s
ha_listen_address = "${POD_IP}:9094"
ha_advertise_address = "${POD_IP}:9094"
ha_peers = ""
ha_peer_timeout = 15s
ha_gossip_interval = 200ms
ha_push_pull_interval = 60s
ha_redis_addr = ""
max_attempts = 3
min_interval = 10s
max_annotation_length = 65536
max_label_length = 65536

[metrics]
enabled = true
interval_seconds = 10
disable_total_stats = false

[tracing]
enabled = false

[feature_toggles]
enable = ngalert,publicDashboards

[live]
max_connections = 100
allowed_origins = []

[geomap]
default_baselayer_config = ""
enable_custom_baselayers = true
