{"dashboard": {"id": null, "title": "Capacity Planning - Heibooky", "tags": ["he<PERSON><PERSON>y", "capacity", "planning", "scaling", "production"], "style": "dark", "timezone": "browser", "time": {"from": "now-7d", "to": "now"}, "refresh": "5m", "schemaVersion": 30, "version": 1, "panels": [{"id": 1, "title": "Resource Utilization Trends", "type": "timeseries", "targets": [{"expr": "100 - (avg by (instance) (rate(node_cpu_seconds_total{mode=\"idle\"}[1h])) * 100)", "legendFormat": "CPU Usage % (1h avg)", "refId": "A"}, {"expr": "(1 - (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes)) * 100", "legendFormat": "Memory Usage %", "refId": "B"}, {"expr": "(1 - (node_filesystem_free_bytes{fstype!=\"tmpfs\"} / node_filesystem_size_bytes{fstype!=\"tmpfs\"})) * 100", "legendFormat": "Disk Usage %", "refId": "C"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"drawStyle": "line", "lineInterpolation": "linear", "lineWidth": 2, "fillOpacity": 10, "showPoints": "never"}, "unit": "percent", "min": 0, "max": 100, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 70}, {"color": "red", "value": 90}]}}}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 0}}, {"id": 2, "title": "Traffic Growth Prediction", "type": "timeseries", "targets": [{"expr": "sum(rate(django_http_requests_total[1h]))", "legendFormat": "Current Request Rate", "refId": "A"}, {"expr": "predict_linear(sum(rate(django_http_requests_total[1h]))[7d:1h], 7*24*3600)", "legendFormat": "7-day Prediction", "refId": "B"}, {"expr": "predict_linear(sum(rate(django_http_requests_total[1h]))[7d:1h], 30*24*3600)", "legendFormat": "30-day Prediction", "refId": "C"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"drawStyle": "line", "lineInterpolation": "linear", "lineWidth": 2, "fillOpacity": 10, "showPoints": "never"}, "unit": "reqps", "min": 0}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}}, {"id": 3, "title": "Storage Growth Analysis", "type": "timeseries", "targets": [{"expr": "prometheus_tsdb_symbol_table_size_bytes / 1024 / 1024", "legendFormat": "Prometheus TSDB (MB)", "refId": "A"}, {"expr": "predict_linear(prometheus_tsdb_symbol_table_size_bytes[7d], 30*24*3600) / 1024 / 1024", "legendFormat": "TSDB 30-day Prediction (MB)", "refId": "B"}, {"expr": "loki_ingester_memory_chunks / 1024 / 1024", "legendFormat": "<PERSON> (MB)", "refId": "C"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"drawStyle": "line", "lineInterpolation": "linear", "lineWidth": 2, "fillOpacity": 10, "showPoints": "never"}, "unit": "decbytes", "min": 0}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}}, {"id": 4, "title": "Saturation Risk Indicators", "type": "stat", "targets": [{"expr": "(\n  (node_filesystem_size_bytes{fstype!=\"tmpfs\"} - node_filesystem_free_bytes{fstype!=\"tmpfs\"}) /\n  predict_linear(node_filesystem_size_bytes{fstype!=\"tmpfs\"}[7d], 30*24*3600)\n) * 100", "legendFormat": "Disk Saturation Risk %", "refId": "A"}, {"expr": "(\n  avg(rate(django_http_requests_total[1h])) /\n  50  # Assuming 50 req/s capacity\n) * 100", "legendFormat": "Traffic Capacity %", "refId": "B"}, {"expr": "(\n  redis_memory_used_bytes /\n  redis_config_maxmemory_bytes\n) * 100", "legendFormat": "Redis Memory %", "refId": "C"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 60}, {"color": "orange", "value": 80}, {"color": "red", "value": 95}]}, "unit": "percent", "min": 0, "max": 100}}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 16}}, {"id": 5, "title": "Database Connection Pool Usage", "type": "timeseries", "targets": [{"expr": "django_db_connections_active", "legendFormat": "Active Connections", "refId": "A"}, {"expr": "django_db_connections_max", "legendFormat": "Max Connections", "refId": "B"}, {"expr": "(django_db_connections_active / django_db_connections_max) * 100", "legendFormat": "Pool Utilization %", "refId": "C"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"drawStyle": "line", "lineInterpolation": "linear", "lineWidth": 2, "fillOpacity": 10, "showPoints": "never"}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Pool Utilization %"}, "properties": [{"id": "unit", "value": "percent"}, {"id": "min", "value": 0}, {"id": "max", "value": 100}]}]}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 24}}, {"id": 6, "title": "Memory Usage Breakdown", "type": "timeseries", "targets": [{"expr": "process_resident_memory_bytes{job=\"django-app\"} / 1024 / 1024", "legendFormat": "<PERSON><PERSON><PERSON> (MB)", "refId": "A"}, {"expr": "redis_memory_used_bytes / 1024 / 1024", "legendFormat": "<PERSON><PERSON> (MB)", "refId": "B"}, {"expr": "process_resident_memory_bytes{job=\"prometheus\"} / 1024 / 1024", "legendFormat": "Prometheus (MB)", "refId": "C"}, {"expr": "process_resident_memory_bytes{job=\"grafana\"} / 1024 / 1024", "legendFormat": "<PERSON><PERSON> (MB)", "refId": "D"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"drawStyle": "line", "lineInterpolation": "linear", "lineWidth": 2, "fillOpacity": 10, "showPoints": "never"}, "unit": "decbytes", "min": 0}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 24}}, {"id": 7, "title": "Scaling Recommendations", "type": "table", "targets": [{"expr": "(\n  100 - (avg by (instance) (rate(node_cpu_seconds_total{mode=\"idle\"}[1h])) * 100)\n) > 80", "legendFormat": "CPU Scale Alert", "refId": "A", "format": "table", "instant": true}, {"expr": "(\n  (1 - (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes)) * 100\n) > 85", "legendFormat": "Memory Scale Alert", "refId": "B", "format": "table", "instant": true}, {"expr": "(\n  (django_db_connections_active / django_db_connections_max) * 100\n) > 80", "legendFormat": "DB Pool Scale Alert", "refId": "C", "format": "table", "instant": true}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "green", "value": null}, {"color": "red", "value": 1}]}, "custom": {"align": "auto", "displayMode": "color-background"}, "mappings": [{"options": {"1": {"text": "SCALE REQUIRED", "color": "red"}}, "type": "value"}]}}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 32}}, {"id": 8, "title": "Cost Optimization Metrics", "type": "timeseries", "targets": [{"expr": "sum(rate(django_http_requests_total[1h])) / (100 - (avg by (instance) (rate(node_cpu_seconds_total{mode=\"idle\"}[1h])) * 100))", "legendFormat": "Requests per CPU %", "refId": "A"}, {"expr": "sum(rate(django_http_requests_total[1h])) / ((1 - (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes)) * 100)", "legendFormat": "Requests per Memory %", "refId": "B"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"drawStyle": "line", "lineInterpolation": "linear", "lineWidth": 2, "fillOpacity": 10, "showPoints": "never"}, "unit": "short", "min": 0}}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 40}}], "templating": {"list": [{"name": "prediction_window", "type": "custom", "options": [{"text": "7 days", "value": "7*24*3600"}, {"text": "30 days", "value": "30*24*3600"}, {"text": "90 days", "value": "90*24*3600"}], "current": {"text": "30 days", "value": "30*24*3600"}}]}, "annotations": {"list": [{"name": "Scaling Events", "datasource": "Prometheus", "enable": true, "expr": "increase(container_spec_cpu_quota[1m]) > 0", "iconColor": "purple", "titleFormat": "Resource Scaling", "textFormat": "CPU/Memory scaling event detected"}]}}, "overwrite": true}