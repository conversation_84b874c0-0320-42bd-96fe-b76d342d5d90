{"dashboard": {"id": null, "title": "Service Health Overview - Heibooky", "tags": ["he<PERSON><PERSON>y", "health", "overview", "monitoring", "production"], "style": "dark", "timezone": "browser", "time": {"from": "now-1h", "to": "now"}, "refresh": "30s", "schemaVersion": 30, "version": 1, "panels": [{"id": 1, "title": "Service Status Overview", "type": "stat", "targets": [{"expr": "up{job=\"django-app\"}", "legendFormat": "Django App", "refId": "A"}, {"expr": "up{job=\"redis\"}", "legendFormat": "Redis", "refId": "B"}, {"expr": "up{job=\"prometheus\"}", "legendFormat": "Prometheus", "refId": "C"}, {"expr": "up{job=\"grafana\"}", "legendFormat": "<PERSON><PERSON>", "refId": "D"}, {"expr": "up{job=\"loki\"}", "legendFormat": "<PERSON>", "refId": "E"}, {"expr": "up{job=\"alertmanager\"}", "legendFormat": "Alert<PERSON><PERSON>", "refId": "F"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "red", "value": null}, {"color": "green", "value": 1}]}, "mappings": [{"options": {"0": {"text": "DOWN", "color": "red"}, "1": {"text": "UP", "color": "green"}}, "type": "value"}], "unit": "none"}}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 0}}, {"id": 2, "title": "Application Health Metrics", "type": "timeseries", "targets": [{"expr": "sum(rate(django_http_requests_total[5m]))", "legendFormat": "Request Rate", "refId": "A"}, {"expr": "sum(rate(django_http_requests_total{status=~\"5..\"}[5m])) / sum(rate(django_http_requests_total[5m])) * 100", "legendFormat": "Error Rate %", "refId": "B"}, {"expr": "histogram_quantile(0.95, sum(rate(django_http_request_duration_seconds_bucket[5m])) by (le))", "legendFormat": "P95 Latency", "refId": "C"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"drawStyle": "line", "lineInterpolation": "linear", "lineWidth": 2, "fillOpacity": 10, "showPoints": "never"}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Request Rate"}, "properties": [{"id": "unit", "value": "reqps"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Error Rate %"}, "properties": [{"id": "unit", "value": "percent"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "P95 Latency"}, "properties": [{"id": "unit", "value": "s"}]}]}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}}, {"id": 3, "title": "Infrastructure Health", "type": "timeseries", "targets": [{"expr": "100 - (avg by (instance) (rate(node_cpu_seconds_total{mode=\"idle\"}[5m])) * 100)", "legendFormat": "CPU Usage %", "refId": "A"}, {"expr": "(1 - (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes)) * 100", "legendFormat": "Memory Usage %", "refId": "B"}, {"expr": "(1 - (node_filesystem_free_bytes{fstype!=\"tmpfs\"} / node_filesystem_size_bytes{fstype!=\"tmpfs\"})) * 100", "legendFormat": "Disk Usage %", "refId": "C"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"drawStyle": "line", "lineInterpolation": "linear", "lineWidth": 2, "fillOpacity": 10, "showPoints": "never"}, "unit": "percent", "min": 0, "max": 100}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}}, {"id": 4, "title": "Database Health (Redis)", "type": "timeseries", "targets": [{"expr": "redis_connected_clients", "legendFormat": "Connected Clients", "refId": "A"}, {"expr": "redis_memory_used_bytes / 1024 / 1024", "legendFormat": "Memory Usage (MB)", "refId": "B"}, {"expr": "rate(redis_commands_processed_total[5m])", "legendFormat": "Commands/sec", "refId": "C"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"drawStyle": "line", "lineInterpolation": "linear", "lineWidth": 2, "fillOpacity": 10, "showPoints": "never"}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Memory Usage (MB)"}, "properties": [{"id": "unit", "value": "decbytes"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Commands/sec"}, "properties": [{"id": "unit", "value": "ops"}]}]}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 16}}, {"id": 5, "title": "Monitoring Stack Health", "type": "timeseries", "targets": [{"expr": "prometheus_tsdb_head_samples_appended_total", "legendFormat": "Prometheus Samples", "refId": "A"}, {"expr": "grafana_stat_totals_dashboard", "legendFormat": "Grafana Dashboards", "refId": "B"}, {"expr": "loki_ingester_received_chunks", "legendFormat": "<PERSON>", "refId": "C"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"drawStyle": "line", "lineInterpolation": "linear", "lineWidth": 2, "fillOpacity": 10, "showPoints": "never"}}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 16}}, {"id": 6, "title": "Active Alerts", "type": "table", "targets": [{"expr": "ALERTS{alertstate=\"firing\"}", "legendFormat": "", "refId": "A", "format": "table", "instant": true}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "green", "value": null}, {"color": "red", "value": 1}]}, "custom": {"align": "auto", "displayMode": "color-background"}}}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 24}}, {"id": 7, "title": "Request Distribution by Status Code", "type": "piechart", "targets": [{"expr": "sum by (status) (rate(django_http_requests_total[5m]))", "legendFormat": "{{status}}", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"hideFrom": {"legend": false, "tooltip": false, "vis": false}}, "unit": "reqps"}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 32}}, {"id": 8, "title": "Top Endpoints by Request Volume", "type": "table", "targets": [{"expr": "topk(10, sum by (method, handler) (rate(django_http_requests_total[5m])))", "legendFormat": "", "refId": "A", "format": "table", "instant": true}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 10}, {"color": "red", "value": 50}]}, "custom": {"align": "auto", "displayMode": "color-background"}, "unit": "reqps"}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 32}}], "templating": {"list": [{"name": "instance", "type": "query", "datasource": "Prometheus", "query": "label_values(up, instance)", "refresh": 1, "includeAll": true, "allValue": ".*", "current": {"text": "All", "value": "$__all"}}]}, "annotations": {"list": [{"name": "Deployments", "datasource": "Prometheus", "enable": true, "expr": "increase(django_app_restarts_total[1m])", "iconColor": "blue", "titleFormat": "App <PERSON><PERSON>", "textFormat": "Application restarted"}]}}, "overwrite": true}