# Monitoring Stack Performance Optimization Configuration
# This file contains performance tuning parameters and best practices

# =============================================================================
# PROMETHEUS PERFORMANCE OPTIMIZATION
# =============================================================================

prometheus:
  # Storage optimization
  storage:
    retention_time: "15d"
    retention_size: "10GB"
    wal_compression: true
    wal_segment_size: "128MB"
    min_block_duration: "2h"
    max_block_duration: "36h"
    
  # Query optimization
  query:
    timeout: "2m"
    max_concurrency: 20
    max_samples: 50000000
    lookback_delta: "5m"
    
  # Scrape optimization
  scrape:
    # High-priority targets (application metrics)
    high_frequency:
      interval: "10s"
      timeout: "8s"
      targets:
        - "django-app"
        - "prometheus"
    
    # Medium-priority targets (infrastructure)
    medium_frequency:
      interval: "20s"
      timeout: "15s"
      targets:
        - "redis"
        - "node"
        - "nginx"
    
    # Low-priority targets (auxiliary services)
    low_frequency:
      interval: "30s"
      timeout: "20s"
      targets:
        - "postgres"
        - "cadvisor"
        - "alertmanager"
        - "grafana"
        - "loki"
  
  # Memory optimization
  memory:
    target_heap_size: "1GB"
    chunks_to_persist: 524288
    max_chunks_to_persist: 1048576
    chunk_encoding: "XOR"
    
  # Recording rules optimization
  recording_rules:
    evaluation_interval: "30s"
    groups:
      - name: "high_frequency"
        interval: "15s"
        rules: ["http_requests", "error_rates", "latency_percentiles"]
      - name: "medium_frequency"
        interval: "30s"
        rules: ["system_metrics", "database_metrics"]
      - name: "low_frequency"
        interval: "60s"
        rules: ["sli_metrics", "capacity_metrics"]

# =============================================================================
# GRAFANA PERFORMANCE OPTIMIZATION
# =============================================================================

grafana:
  # Database optimization
  database:
    type: "sqlite3"  # Consider PostgreSQL for high-load environments
    max_idle_conn: 10
    max_open_conn: 100
    conn_max_lifetime: "14400s"
    wal_enabled: true
    cache_mode: "shared"
    query_timeout: "30s"
    
  # Query optimization
  query:
    timeout: "30s"
    max_data_points: 1000
    interval_factor: 2
    resolution: "1/1"
    
  # Caching optimization
  caching:
    enabled: true
    ttl: "5m"
    max_cache_size: "100MB"
    
  # Dashboard optimization
  dashboards:
    default_refresh: "30s"
    min_refresh_interval: "5s"
    max_data_points: 1000
    query_timeout: "30s"
    
  # Rendering optimization
  rendering:
    concurrent_limit: 30
    timeout: "20s"
    ignore_https_errors: false
    
  # Session optimization
  session:
    provider: "file"  # Consider Redis for multi-instance setups
    gc_interval: "86400s"
    
  # Live features optimization
  live:
    max_connections: 100
    allowed_origins: []

# =============================================================================
# LOKI PERFORMANCE OPTIMIZATION
# =============================================================================

loki:
  # Ingestion optimization
  ingestion:
    rate_mb: 4
    burst_size_mb: 6
    max_line_size: 256000
    
  # Query optimization
  query:
    timeout: "1m"
    max_concurrent: 32
    max_entries_limit: 5000
    split_queries_by_interval: "30m"
    
  # Storage optimization
  storage:
    chunk_idle_period: "1h"
    max_chunk_age: "1h"
    chunk_target_size: 1048576
    chunk_retain_period: "30s"
    
  # Caching optimization
  caching:
    results_cache:
      enabled: true
      max_size_mb: 500
      ttl: "1h"
    chunk_cache:
      enabled: true
      max_size_mb: 1000
      ttl: "24h"
      
  # Compaction optimization
  compaction:
    interval: "10m"
    retention_enabled: true
    retention_period: "336h"  # 14 days
    delete_delay: "2h"
    worker_count: 150

# =============================================================================
# ALERTMANAGER PERFORMANCE OPTIMIZATION
# =============================================================================

alertmanager:
  # Grouping optimization
  grouping:
    group_wait: "30s"
    group_interval: "5m"
    repeat_interval: "12h"
    
  # Notification optimization
  notifications:
    timeout: "10s"
    max_attempts: 3
    retry_interval: "1m"
    
  # Storage optimization
  storage:
    retention: "120h"  # 5 days
    
  # Clustering optimization (for HA setups)
  clustering:
    peer_timeout: "15s"
    gossip_interval: "200ms"
    push_pull_interval: "60s"

# =============================================================================
# CONTAINER RESOURCE OPTIMIZATION
# =============================================================================

containers:
  # Resource limits based on workload
  prometheus:
    memory_limit: "2GB"
    memory_request: "1GB"
    cpu_limit: "2.0"
    cpu_request: "0.5"
    
  grafana:
    memory_limit: "1GB"
    memory_request: "512MB"
    cpu_limit: "1.0"
    cpu_request: "0.25"
    
  loki:
    memory_limit: "2GB"
    memory_request: "1GB"
    cpu_limit: "1.5"
    cpu_request: "0.5"
    
  alertmanager:
    memory_limit: "512MB"
    memory_request: "256MB"
    cpu_limit: "0.5"
    cpu_request: "0.1"
    
  # Exporters (lightweight)
  exporters:
    memory_limit: "128MB"
    memory_request: "64MB"
    cpu_limit: "0.2"
    cpu_request: "0.05"

# =============================================================================
# NETWORK OPTIMIZATION
# =============================================================================

network:
  # Connection pooling
  connection_pooling:
    max_idle_connections: 100
    max_idle_connections_per_host: 10
    idle_connection_timeout: "90s"
    
  # Timeouts
  timeouts:
    dial_timeout: "10s"
    tls_handshake_timeout: "10s"
    response_header_timeout: "30s"
    expect_continue_timeout: "1s"
    
  # Compression
  compression:
    enabled: true
    level: 6  # Balance between compression ratio and CPU usage

# =============================================================================
# MONITORING PERFORMANCE METRICS
# =============================================================================

performance_metrics:
  # Key performance indicators to track
  kpis:
    - name: "query_duration_95th_percentile"
      target: "< 500ms"
      alert_threshold: "1s"
      
    - name: "dashboard_load_time"
      target: "< 2s"
      alert_threshold: "5s"
      
    - name: "ingestion_rate"
      target: "> 1000 samples/sec"
      alert_threshold: "< 500 samples/sec"
      
    - name: "memory_usage"
      target: "< 80%"
      alert_threshold: "> 90%"
      
    - name: "disk_usage"
      target: "< 70%"
      alert_threshold: "> 85%"
      
    - name: "query_success_rate"
      target: "> 99%"
      alert_threshold: "< 95%"

# =============================================================================
# OPTIMIZATION BEST PRACTICES
# =============================================================================

best_practices:
  # Query optimization
  queries:
    - "Use recording rules for frequently used complex queries"
    - "Limit time ranges for dashboard queries"
    - "Use appropriate step intervals"
    - "Avoid high-cardinality label combinations"
    - "Use rate() instead of increase() for counters"
    
  # Dashboard optimization
  dashboards:
    - "Set appropriate refresh intervals (30s minimum)"
    - "Limit the number of panels per dashboard"
    - "Use template variables to reduce query load"
    - "Implement proper time range controls"
    - "Cache dashboard JSON in version control"
    
  # Storage optimization
  storage:
    - "Monitor cardinality growth"
    - "Implement proper retention policies"
    - "Use appropriate block durations"
    - "Enable WAL compression"
    - "Regular cleanup of old data"
    
  # Resource optimization
  resources:
    - "Monitor container resource usage"
    - "Set appropriate resource limits"
    - "Use horizontal scaling when needed"
    - "Implement proper health checks"
    - "Regular performance testing"

# =============================================================================
# PERFORMANCE TESTING CONFIGURATION
# =============================================================================

performance_testing:
  # Load testing scenarios
  scenarios:
    - name: "dashboard_load_test"
      description: "Test dashboard loading under various loads"
      concurrent_users: [10, 50, 100, 200]
      duration: "5m"
      
    - name: "query_performance_test"
      description: "Test query performance with different complexities"
      query_types: ["simple", "complex", "aggregation", "recording_rule"]
      time_ranges: ["1h", "6h", "24h", "7d"]
      
    - name: "ingestion_load_test"
      description: "Test metrics ingestion under high load"
      ingestion_rates: [1000, 5000, 10000, 20000]  # samples per second
      duration: "10m"
      
  # Performance benchmarks
  benchmarks:
    query_latency_p95: "500ms"
    query_latency_p99: "1s"
    dashboard_load_time: "2s"
    ingestion_throughput: "10000 samples/sec"
    memory_efficiency: "< 2GB for 1M active series"
    storage_efficiency: "< 1.5 bytes per sample"
