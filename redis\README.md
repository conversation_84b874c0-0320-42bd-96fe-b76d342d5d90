# Redis Security Setup

This directory contains the secure Redis ACL configuration setup for the BackTrack project.

## Files

- `users.acl.template` - Template file with placeholder passwords (safe to commit)
- `users.acl` - Generated file with actual passwords (DO NOT COMMIT - in .gitignore)
- `redis.conf.template` - Template file with placeholder command renames (safe to commit)
- `redis.conf` - Generated file with actual random command renames (DO NOT COMMIT - in .gitignore)

## Setup Instructions

### For Unix/Linux/macOS:
```bash
cd /path/to/BackTrack
./scripts/setup_redis_security.sh
```

### For Windows PowerShell:
```powershell
cd C:\path\to\BackTrack
.\scripts\setup_redis_security.ps1
```

## What the setup script does:

1. **Generates secure random passwords** for all Redis users (25 characters each)
2. **Generates random command renames** for administrative commands (64 characters each)
3. **Creates `users.acl`** from the template with actual passwords
4. **Creates `redis.conf`** from the template with random command renames
5. **Updates `secrets/redis_password.txt`** with the Django app password
6. **Creates `secrets/redis_command_renames.txt`** with admin command reference
7. **Displays all generated passwords and renames** (save these in your password manager!)

## Security Features

- **Default user disabled** for security
- **Role-based access control** with minimal required permissions
- **Strong random passwords** (25 characters each)
- **Random command renames** (64 characters each) for administrative commands
- **Template-based approach** prevents hardcoded passwords and predictable renames in version control
- **Dangerous commands disabled** completely (FLUSHDB, FLUSHALL, KEYS, DEBUG, EVAL, SCRIPT)
- **Administrative commands renamed** with unpredictable strings (CONFIG, SHUTDOWN)

## User Roles and Permissions

| User | Purpose | Key Pattern | Commands |
|------|---------|-------------|----------|
| `django_app` | Main Django application | `~*` | All except dangerous operations |
| `celery_worker` | Celery task processing | `~*` | All except dangerous operations |
| `celery_beat` | Celery task scheduling | `~*` | All except dangerous operations |
| `monitoring` | Health checks & monitoring | `~monitor:*, ~stats:*` | Read-only, ping, info, client |
| `session_manager` | Session storage | `~session:*` | Session operations only |
| `cache_user` | Django cache operations | `~cache:*` | Cache operations only |
| `admin` | Administrative tasks | `~*` | Full access (use sparingly) |

## Important Notes

- **Never commit `users.acl` or `redis.conf`** - they contain real passwords and command renames
- **Save all passwords and command renames** in a secure password manager
- **The `django_app` password** is automatically written to `secrets/redis_password.txt`
- **Command renames are saved** in `secrets/redis_command_renames.txt` for admin reference
- **Run the setup script** before first deployment or when rotating passwords/renames
- **Both config files are in .gitignore** to prevent accidental commits

## Using Renamed Commands

Administrators can use the renamed commands by referencing the file `secrets/redis_command_renames.txt`:

```bash
# Example: Check Redis configuration
redis-cli --no-auth-warning -u redis://admin:PASSWORD@localhost:6379 [RANDOM_CONFIG_NAME] GET save

# Example: Shutdown Redis (emergency only)
redis-cli --no-auth-warning -u redis://admin:PASSWORD@localhost:6379 [RANDOM_SHUTDOWN_NAME]
```

**Note:** Replace `[RANDOM_CONFIG_NAME]` and `[RANDOM_SHUTDOWN_NAME]` with the actual values from `secrets/redis_command_renames.txt`

## Password Rotation

To rotate passwords:
1. Run the setup script again
2. Update any external references to the old passwords
3. Restart the Redis container and dependent services

## Troubleshooting

If Redis fails to start:
1. Check that `users.acl` exists and has proper permissions
2. Verify password format (no special characters that break ACL syntax)
3. Check Redis logs for ACL-related errors
4. Ensure the monitoring user password matches health check configuration
