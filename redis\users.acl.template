# Redis ACL Configuration Template
# Default user - disabled for security
user default off

# Django application user
user django_app on >{{DJANGO_PASSWORD}} ~* &* +@all -@dangerous -flushdb -flushall -shutdown -debug -eval -script -config -keys

# Admin user for maintenance (use strong password)
user admin on >{{REDIS_ADMIN_PASSWORD}} ~* &* +@all

# Read-only user for monitoring
user monitoring on >{{MONITORING_PASSWORD}} ~monitor:* ~stats:* &* +@read +ping +info +client

# Celery worker user (specific permissions for task processing)
user celery_worker on >{{CELERY_WORKER_PASSWORD}} ~* &* +@all -@dangerous -flushdb -flushall -shutdown -debug -eval -script -config -keys

# Celery beat user (specific permissions for scheduling tasks)
user celery_beat on >{{CELERY_BEAT_PASSWORD}} ~* &* +@all -@dangerous -flushdb -flushall -shutdown -debug -eval -script -config -keys

# Session management user (limited to session operations)
user session_manager on >{{SESSION_PASSWORD}} ~session:* &* +@read +@write +@list +@hash +@string +ping +ttl +expire +del

# Cache user (for Django cache operations)
user cache_user on >{{CACHE_PASSWORD}} ~cache:* &* +@read +@write +@string +@hash +@list +@set +ping +ttl +expire +del
