#!/bin/bash

# Health Monitoring and Recovery Script for Heibooky Monitoring Stack
# This script continuously monitors service health and performs automatic recovery

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"
HEALTH_LOG="$PROJECT_DIR/logs/health-monitor.log"
RECOVERY_LOG="$PROJECT_DIR/logs/recovery.log"
ALERT_LOG="$PROJECT_DIR/logs/alerts.log"

# Service configuration
SERVICES=(
    "prometheus:9090:/-/healthy"
    "grafana:3000:/api/health"
    "loki:3100:/ready"
    "alertmanager:9093:/-/healthy"
    "redis:6379:ping"
)

# Recovery configuration
MAX_RECOVERY_ATTEMPTS=3
RECOVERY_COOLDOWN=300  # 5 minutes
HEALTH_CHECK_TIMEOUT=10
CONSECUTIVE_FAILURES_THRESHOLD=3

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Create logs directory if it doesn't exist
mkdir -p "$PROJECT_DIR/logs"

# Logging functions
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$HEALTH_LOG"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$HEALTH_LOG" "$ALERT_LOG"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1" | tee -a "$HEALTH_LOG"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a "$HEALTH_LOG" "$ALERT_LOG"
}

recovery_log() {
    echo -e "${BLUE}[RECOVERY]${NC} $1" | tee -a "$RECOVERY_LOG"
}

# Function to check if Docker is running
check_docker() {
    if ! docker info >/dev/null 2>&1; then
        error "Docker is not running or not accessible"
        return 1
    fi
    return 0
}

# Function to check service health via HTTP
check_http_health() {
    local service="$1"
    local port="$2"
    local endpoint="$3"
    local url="http://${service}:${port}${endpoint}"
    
    if curl -f -s --max-time "$HEALTH_CHECK_TIMEOUT" "$url" >/dev/null 2>&1; then
        return 0
    else
        return 1
    fi
}

# Function to check Redis health
check_redis_health() {
    local service="$1"
    local port="$2"
    
    if timeout "$HEALTH_CHECK_TIMEOUT" redis-cli -h "$service" -p "$port" ping >/dev/null 2>&1; then
        return 0
    else
        return 1
    fi
}

# Function to check individual service health
check_service_health() {
    local service_config="$1"
    IFS=':' read -r service port endpoint <<< "$service_config"
    
    local container_name
    case "$service" in
        "prometheus") container_name="prometheus" ;;
        "grafana") container_name="grafana" ;;
        "loki") container_name="loki" ;;
        "alertmanager") container_name="alertmanager" ;;
        "redis") container_name="redis" ;;
        *) container_name="$service" ;;
    esac
    
    # Check if container is running
    if ! docker ps --format "table {{.Names}}" | grep -q "^${container_name}$"; then
        error "Container $container_name is not running"
        return 1
    fi
    
    # Check container health status
    local container_health
    container_health=$(docker inspect --format='{{.State.Health.Status}}' "$container_name" 2>/dev/null || echo "unknown")
    
    if [ "$container_health" = "unhealthy" ]; then
        error "Container $container_name is unhealthy"
        return 1
    fi
    
    # Check service-specific health
    case "$service" in
        "redis")
            if check_redis_health "$service" "$port"; then
                success "$service health check passed"
                return 0
            else
                error "$service health check failed"
                return 1
            fi
            ;;
        *)
            if check_http_health "$service" "$port" "$endpoint"; then
                success "$service health check passed"
                return 0
            else
                error "$service health check failed"
                return 1
            fi
            ;;
    esac
}

# Function to get service failure count
get_failure_count() {
    local service="$1"
    local failure_file="$PROJECT_DIR/logs/.${service}_failures"
    
    if [ -f "$failure_file" ]; then
        cat "$failure_file"
    else
        echo "0"
    fi
}

# Function to increment service failure count
increment_failure_count() {
    local service="$1"
    local failure_file="$PROJECT_DIR/logs/.${service}_failures"
    local current_count
    
    current_count=$(get_failure_count "$service")
    echo $((current_count + 1)) > "$failure_file"
}

# Function to reset service failure count
reset_failure_count() {
    local service="$1"
    local failure_file="$PROJECT_DIR/logs/.${service}_failures"
    
    rm -f "$failure_file"
}

# Function to check if service is in recovery cooldown
is_in_cooldown() {
    local service="$1"
    local cooldown_file="$PROJECT_DIR/logs/.${service}_cooldown"
    
    if [ -f "$cooldown_file" ]; then
        local cooldown_time
        cooldown_time=$(cat "$cooldown_file")
        local current_time
        current_time=$(date +%s)
        
        if [ $((current_time - cooldown_time)) -lt "$RECOVERY_COOLDOWN" ]; then
            return 0  # In cooldown
        else
            rm -f "$cooldown_file"
            return 1  # Not in cooldown
        fi
    else
        return 1  # Not in cooldown
    fi
}

# Function to set recovery cooldown
set_cooldown() {
    local service="$1"
    local cooldown_file="$PROJECT_DIR/logs/.${service}_cooldown"
    
    date +%s > "$cooldown_file"
}

# Function to restart service
restart_service() {
    local service="$1"
    
    recovery_log "Attempting to restart $service"
    
    cd "$PROJECT_DIR"
    
    # Stop the service
    if docker-compose stop "$service"; then
        recovery_log "Successfully stopped $service"
    else
        error "Failed to stop $service"
        return 1
    fi
    
    # Wait a moment
    sleep 5
    
    # Start the service
    if docker-compose up -d "$service"; then
        recovery_log "Successfully started $service"
        
        # Wait for service to be ready
        sleep 30
        
        return 0
    else
        error "Failed to start $service"
        return 1
    fi
}

# Function to perform service recovery
recover_service() {
    local service="$1"
    
    if is_in_cooldown "$service"; then
        warning "$service is in recovery cooldown, skipping recovery"
        return 1
    fi
    
    local recovery_attempts_file="$PROJECT_DIR/logs/.${service}_recovery_attempts"
    local recovery_attempts=0
    
    if [ -f "$recovery_attempts_file" ]; then
        recovery_attempts=$(cat "$recovery_attempts_file")
    fi
    
    if [ "$recovery_attempts" -ge "$MAX_RECOVERY_ATTEMPTS" ]; then
        error "$service has exceeded maximum recovery attempts ($MAX_RECOVERY_ATTEMPTS)"
        set_cooldown "$service"
        return 1
    fi
    
    recovery_log "Starting recovery for $service (attempt $((recovery_attempts + 1))/$MAX_RECOVERY_ATTEMPTS)"
    
    # Increment recovery attempts
    echo $((recovery_attempts + 1)) > "$recovery_attempts_file"
    
    # Attempt restart
    if restart_service "$service"; then
        success "Successfully recovered $service"
        rm -f "$recovery_attempts_file"
        reset_failure_count "$service"
        return 0
    else
        error "Failed to recover $service"
        set_cooldown "$service"
        return 1
    fi
}

# Function to send alert notification
send_alert() {
    local service="$1"
    local status="$2"
    local message="$3"
    
    local alert_payload
    alert_payload=$(cat <<EOF
{
    "service": "$service",
    "status": "$status",
    "message": "$message",
    "timestamp": "$(date -Iseconds)",
    "hostname": "$(hostname)",
    "environment": "production"
}
EOF
)
    
    echo "$alert_payload" >> "$ALERT_LOG"
    
    # Send to Alertmanager if available
    if curl -f -s --max-time 5 "http://alertmanager:9093/-/healthy" >/dev/null 2>&1; then
        curl -s -X POST "http://alertmanager:9093/api/v1/alerts" \
            -H "Content-Type: application/json" \
            -d "[$alert_payload]" >/dev/null 2>&1 || true
    fi
    
    # Log the alert
    case "$status" in
        "critical")
            error "ALERT: $service - $message"
            ;;
        "warning")
            warning "ALERT: $service - $message"
            ;;
        "resolved")
            success "ALERT RESOLVED: $service - $message"
            ;;
    esac
}

# Function to check all services
check_all_services() {
    log "Starting health check cycle"
    
    local failed_services=()
    local recovered_services=()
    
    for service_config in "${SERVICES[@]}"; do
        IFS=':' read -r service port endpoint <<< "$service_config"
        
        if check_service_health "$service_config"; then
            # Service is healthy
            local failure_count
            failure_count=$(get_failure_count "$service")
            
            if [ "$failure_count" -gt 0 ]; then
                # Service recovered
                success "$service has recovered after $failure_count failures"
                reset_failure_count "$service"
                recovered_services+=("$service")
                send_alert "$service" "resolved" "Service has recovered and is now healthy"
            fi
        else
            # Service is unhealthy
            increment_failure_count "$service"
            local failure_count
            failure_count=$(get_failure_count "$service")
            
            warning "$service failed health check (failure count: $failure_count)"
            failed_services+=("$service")
            
            if [ "$failure_count" -ge "$CONSECUTIVE_FAILURES_THRESHOLD" ]; then
                error "$service has failed $failure_count consecutive health checks"
                send_alert "$service" "critical" "Service has failed $failure_count consecutive health checks"
                
                # Attempt recovery if enabled
                if [ "${RECOVERY_ENABLED:-true}" = "true" ]; then
                    if recover_service "$service"; then
                        recovered_services+=("$service")
                        send_alert "$service" "resolved" "Service has been automatically recovered"
                    else
                        send_alert "$service" "critical" "Automatic recovery failed for service"
                    fi
                fi
            elif [ "$failure_count" -eq 1 ]; then
                send_alert "$service" "warning" "Service health check failed"
            fi
        fi
    done
    
    # Summary
    if [ ${#failed_services[@]} -eq 0 ] && [ ${#recovered_services[@]} -eq 0 ]; then
        log "All services are healthy"
    else
        if [ ${#failed_services[@]} -gt 0 ]; then
            warning "Failed services: ${failed_services[*]}"
        fi
        if [ ${#recovered_services[@]} -gt 0 ]; then
            success "Recovered services: ${recovered_services[*]}"
        fi
    fi
    
    log "Health check cycle completed"
}

# Function to generate health report
generate_health_report() {
    local report_file="$PROJECT_DIR/logs/health_report_$(date +%Y%m%d_%H%M%S).json"
    
    local report_data="{"
    report_data+='"timestamp": "'$(date -Iseconds)'",'
    report_data+='"hostname": "'$(hostname)'",'
    report_data+='"services": ['
    
    local first=true
    for service_config in "${SERVICES[@]}"; do
        IFS=':' read -r service port endpoint <<< "$service_config"
        
        if [ "$first" = true ]; then
            first=false
        else
            report_data+=","
        fi
        
        local status="unknown"
        local failure_count
        failure_count=$(get_failure_count "$service")
        
        if check_service_health "$service_config" >/dev/null 2>&1; then
            status="healthy"
        else
            status="unhealthy"
        fi
        
        report_data+='{'
        report_data+='"name": "'$service'",'
        report_data+='"status": "'$status'",'
        report_data+='"failure_count": '$failure_count','
        report_data+='"in_cooldown": '$(is_in_cooldown "$service" && echo "true" || echo "false")
        report_data+='}'
    done
    
    report_data+=']}'
    
    echo "$report_data" | jq '.' > "$report_file"
    log "Health report generated: $report_file"
}

# Main execution
main() {
    if ! check_docker; then
        error "Docker check failed, exiting"
        exit 1
    fi
    
    case "${1:-monitor}" in
        "monitor")
            check_all_services
            ;;
        "report")
            generate_health_report
            ;;
        "reset")
            local service="${2:-all}"
            if [ "$service" = "all" ]; then
                rm -f "$PROJECT_DIR/logs/."*_failures
                rm -f "$PROJECT_DIR/logs/."*_cooldown
                rm -f "$PROJECT_DIR/logs/."*_recovery_attempts
                success "Reset all service failure counts and cooldowns"
            else
                reset_failure_count "$service"
                rm -f "$PROJECT_DIR/logs/.${service}_cooldown"
                rm -f "$PROJECT_DIR/logs/.${service}_recovery_attempts"
                success "Reset failure count and cooldown for $service"
            fi
            ;;
        "status")
            for service_config in "${SERVICES[@]}"; do
                IFS=':' read -r service port endpoint <<< "$service_config"
                local failure_count
                failure_count=$(get_failure_count "$service")
                local in_cooldown
                in_cooldown=$(is_in_cooldown "$service" && echo "yes" || echo "no")
                
                echo "$service: failures=$failure_count, cooldown=$in_cooldown"
            done
            ;;
        "help"|*)
            echo "Health Monitor for Heibooky Monitoring Stack"
            echo ""
            echo "Usage: $0 [command]"
            echo ""
            echo "Commands:"
            echo "  monitor  - Run health checks and recovery (default)"
            echo "  report   - Generate health report"
            echo "  reset    - Reset failure counts and cooldowns"
            echo "  status   - Show current service status"
            echo "  help     - Show this help message"
            echo ""
            echo "Environment Variables:"
            echo "  RECOVERY_ENABLED - Enable automatic recovery (default: true)"
            echo "  HEALTH_CHECK_INTERVAL - Health check interval in seconds (default: 60)"
            ;;
    esac
}

# Run main function with all arguments
main "$@"
