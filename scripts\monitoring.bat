@echo off
setlocal enabledelayedexpansion

REM Heibooky Monitoring Stack Management Script for Windows

set "SCRIPT_DIR=%~dp0"
set "PROJECT_DIR=%SCRIPT_DIR%.."

REM Function to print colored output (basic Windows version)
:print_status
echo [INFO] %~1
goto :eof

:print_warning
echo [WARNING] %~1
goto :eof

:print_error
echo [ERROR] %~1
goto :eof

:print_header
echo [HEIBOOKY MONITORING] %~1
goto :eof

REM Function to check if Docker is running
:check_docker
docker info >nul 2>&1
if errorlevel 1 (
    call :print_error "Docker is not running. Please start Docker and try again."
    exit /b 1
)
goto :eof

REM Function to start monitoring stack
:start_monitoring
call :print_header "Starting Heibooky Monitoring Stack..."

call :check_docker
if errorlevel 1 exit /b 1

cd /d "%PROJECT_DIR%"

call :print_status "Starting Prometheus..."
docker-compose up -d prometheus

call :print_status "Starting Grafana..."
docker-compose up -d grafana

call :print_status "Starting Alertmanager..."
docker-compose up -d alertmanager

call :print_status "Starting exporters..."
docker-compose up -d redis-exporter node-exporter cadvisor

call :print_status "Starting log aggregation..."
docker-compose up -d loki promtail

echo.
call :print_header "Monitoring Stack Started Successfully!"
echo.
call :print_status "Service URLs:"
echo   • Grafana: http://localhost:3000 (admin/admin123)
echo   • Prometheus: http://localhost:9090
echo   • Alertmanager: http://localhost:9093
echo   • cAdvisor: http://localhost:8080
echo.
call :print_status "Waiting for services to be ready..."
timeout /t 10 /nobreak >nul

call :check_services
goto :eof

REM Function to stop monitoring stack
:stop_monitoring
call :print_header "Stopping Heibooky Monitoring Stack..."

cd /d "%PROJECT_DIR%"

docker-compose stop prometheus grafana alertmanager redis-exporter node-exporter cadvisor loki promtail

call :print_status "Monitoring stack stopped."
goto :eof

REM Function to restart monitoring stack
:restart_monitoring
call :print_header "Restarting Heibooky Monitoring Stack..."
call :stop_monitoring
timeout /t 5 /nobreak >nul
call :start_monitoring
goto :eof

REM Function to check service status
:check_services
call :print_header "Checking Service Status..."

for %%s in (prometheus:9090 grafana:3000 alertmanager:9093 loki:3100) do (
    for /f "tokens=1,2 delims=:" %%a in ("%%s") do (
        curl -s "http://localhost:%%b" >nul 2>&1
        if !errorlevel! equ 0 (
            call :print_status "%%a is running ✓"
        ) else (
            call :print_warning "%%a is not responding on port %%b"
        )
    )
)
goto :eof

REM Function to view logs
:view_logs
set "service=%~1"
if "%service%"=="" set "service=all"

call :print_header "Viewing logs for: %service%"

cd /d "%PROJECT_DIR%"

if "%service%"=="all" (
    docker-compose logs -f prometheus grafana alertmanager
) else (
    docker-compose logs -f %service%
)
goto :eof

REM Function to show resource usage
:show_resources
call :print_header "Monitoring Stack Resource Usage"

cd /d "%PROJECT_DIR%"

echo.
call :print_status "Container Resource Usage:"
for /f "tokens=*" %%i in ('docker-compose ps -q prometheus grafana alertmanager redis-exporter node-exporter cadvisor loki promtail 2^>nul') do (
    docker stats --no-stream --format "{{.Container}} {{.CPUPerc}} {{.MemUsage}} {{.MemPerc}}" %%i
)

echo.
call :print_status "Volume Usage:"
docker system df -v | findstr /i "prometheus_data grafana_data alertmanager_data loki_data"
goto :eof

REM Main script logic
if "%1"=="start" (
    call :start_monitoring
) else if "%1"=="stop" (
    call :stop_monitoring
) else if "%1"=="restart" (
    call :restart_monitoring
) else if "%1"=="status" (
    call :check_services
) else if "%1"=="logs" (
    call :view_logs "%2"
) else if "%1"=="resources" (
    call :show_resources
) else if "%1"=="help" (
    echo Heibooky Monitoring Stack Management
    echo.
    echo Usage: %0 [COMMAND]
    echo.
    echo Commands:
    echo   start      Start the monitoring stack
    echo   stop       Stop the monitoring stack
    echo   restart    Restart the monitoring stack
    echo   status     Check service status
    echo   logs       View logs (optionally specify service name)
    echo   resources  Show resource usage
    echo   help       Show this help message
    echo.
    echo Examples:
    echo   %0 start
    echo   %0 logs grafana
) else (
    call :print_error "Unknown command: %1"
    echo Use '%0 help' for usage information.
    exit /b 1
)
