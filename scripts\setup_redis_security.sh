#!/bin/bash

# Redis Security Setup Script
# This script helps generate secure passwords and update ACL configuration

echo "Redis Security Setup"
echo "===================="
echo ""

# Function to generate random password
generate_password() {
    openssl rand -base64 32 | tr -d "=+/" | cut -c1-25
}

# Function to generate random command rename (longer for security)
generate_command_rename() {
    openssl rand -base64 48 | tr -d "=+/" | cut -c1-64
}

# Generate passwords
REDIS_ADMIN_PASSWORD=$(generate_password)
DJANGO_PASSWORD=$(generate_password)
CELERY_WORKER_PASSWORD=$(generate_password)
CELERY_BEAT_PASSWORD=$(generate_password)
MONITORING_PASSWORD=$(generate_password)
SESSION_PASSWORD=$(generate_password)
CACHE_PASSWORD=$(generate_password)

# Generate random command renames
CONFIG_RENAME=$(generate_command_rename)
SHUTDOWN_RENAME=$(generate_command_rename)

echo "Generated secure passwords:"
echo "=========================="
echo "Admin Password: $REDIS_ADMIN_PASSWORD"
echo "Django App Password: $DJANGO_PASSWORD"
echo "Celery Worker Password: $CELERY_WORKER_PASSWORD"
echo "Celery Beat Password: $CELERY_BEAT_PASSWORD"
echo "Monitoring Password: $MONITORING_PASSWORD"
echo "Session Manager Password: $SESSION_PASSWORD"
echo "Cache User Password: $CACHE_PASSWORD"
echo ""
echo "Generated command renames (for admin use):"
echo "=========================================="
echo "CONFIG renamed to: $CONFIG_RENAME"
echo "SHUTDOWN renamed to: $SHUTDOWN_RENAME"
echo ""

# Ensure secrets directory exists
mkdir -p ./secrets

# Update redis password file
echo "$DJANGO_PASSWORD" > ./secrets/redis_password.txt

# Ensure redis directory exists
mkdir -p ./redis

# Create updated ACL file from template with actual passwords
if [ ! -f "./redis/users.acl.template" ]; then
    echo "Error: users.acl.template not found in ./redis/ directory"
    echo "Please ensure the template file exists before running this script."
    exit 1
fi

# Generate ACL file from template
sed -e "s/{{DJANGO_PASSWORD}}/$DJANGO_PASSWORD/g" \
    -e "s/{{REDIS_ADMIN_PASSWORD}}/$REDIS_ADMIN_PASSWORD/g" \
    -e "s/{{MONITORING_PASSWORD}}/$MONITORING_PASSWORD/g" \
    -e "s/{{CELERY_WORKER_PASSWORD}}/$CELERY_WORKER_PASSWORD/g" \
    -e "s/{{CELERY_BEAT_PASSWORD}}/$CELERY_BEAT_PASSWORD/g" \
    -e "s/{{SESSION_PASSWORD}}/$SESSION_PASSWORD/g" \
    -e "s/{{CACHE_PASSWORD}}/$CACHE_PASSWORD/g" \
    ./redis/users.acl.template > ./redis/users.acl

# Generate Redis config file from template
if [ ! -f "./redis/redis.conf.template" ]; then
    echo "Error: redis.conf.template not found in ./redis/ directory"
    echo "Please ensure the template file exists before running this script."
    exit 1
fi

sed -e "s/{{CONFIG_RENAME}}/$CONFIG_RENAME/g" \
    -e "s/{{SHUTDOWN_RENAME}}/$SHUTDOWN_RENAME/g" \
    ./redis/redis.conf.template > ./redis/redis.conf

# Create secure file with command renames for admin reference
cat > ./secrets/redis_command_renames.txt << EOF
# Redis Command Renames - Keep this file secure!
# Generated on: $(date)

CONFIG renamed to: $CONFIG_RENAME
SHUTDOWN renamed to: $SHUTDOWN_RENAME

# Usage examples:
# To use CONFIG command: redis-cli CONFIG_RENAME GET save
# To use SHUTDOWN command: redis-cli SHUTDOWN_RENAME

# Note: Other dangerous commands are completely disabled:
# FLUSHDB, FLUSHALL, KEYS, DEBUG, EVAL, SCRIPT
EOF

echo "Files updated:"
echo "=============="
echo "✓ ./secrets/redis_password.txt - Updated with Django app password"
echo "✓ ./redis/users.acl - Updated with all generated passwords"
echo "✓ ./redis/redis.conf - Updated with random command renames"
echo "✓ ./secrets/redis_command_renames.txt - Admin reference for renamed commands"
echo ""
echo "IMPORTANT: Save these passwords and command renames in your password manager!"
echo "The django_app password is automatically set in redis_password.txt"
echo "Command rename strings are saved in secrets/redis_command_renames.txt"
echo ""
echo "Environment variables to set (if needed):"
echo "========================================="
echo "REDIS_PASSWORD_FILE=/run/secrets/redis_password"
echo "REDIS_USERNAME=django_app"
echo "REDIS_HOST=redis"
echo "REDIS_PORT=6379"
echo "REDIS_DB=0"
echo ""
echo "Setup complete! You can now start your Redis container."
